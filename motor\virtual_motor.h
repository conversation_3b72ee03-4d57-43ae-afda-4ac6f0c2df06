/*
	Copyright 2019 Maximiliano Cordoba	<EMAIL>

	This file is part of the VESC firmware.

	The VESC firmware is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    The VESC firmware is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <http://www.gnu.org/licenses/>.
    */

/*
 * 虚拟电机头文件 (Virtual Motor Header)
 *
 * 本文件定义了虚拟电机模拟器的接口函数。
 * 虚拟电机是一个软件模拟的电机模型，用于：
 * - 算法开发和测试
 * - 系统仿真和验证
 * - 无实际电机的调试环境
 *
 * 主要功能模块：
 * 1. 电机数学模型 - 基于电机方程的实时仿真
 * 2. 动态响应 - 模拟真实电机的动态特性
 * 3. 传感器仿真 - 虚拟编码器和霍尔传感器
 * 4. 负载仿真 - 可配置的负载特性
 * 5. 故障注入 - 用于测试故障处理逻辑
 */


#ifndef VIRTUAL_MOTOR_H_
#define VIRTUAL_MOTOR_H_

#include "datatypes.h"

void virtual_motor_init(volatile mc_configuration *conf);
void virtual_motor_set_configuration(volatile mc_configuration *conf);
void virtual_motor_int_handler(float v_alpha, float v_beta);
bool virtual_motor_is_connected(void);
float virtual_motor_get_angle_deg(void);
#endif /* VIRTUAL_MOTOR_H_ */
