/*
	Copyright 2016 - 2022 <PERSON>	ben<PERSON><EMAIL>

	This file is part of the VESC firmware.

	The VESC firmware is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    The VESC firmware is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

/*
 * FOC电机控制PWM实现文件 (FOC Motor Control PWM Implementation)
 *
 * 本文件实现了VESC的磁场定向控制(Field Oriented Control, FOC)算法。
 * FOC是现代高性能电机控制的核心技术，通过数学变换将复杂的三相交流
 * 电机控制转换为简单的直流电机控制。
 *
 * 核心算法实现：
 *
 * 1. <PERSON>变换 (abc → αβ坐标变换)
 *    将三相静止坐标系转换为两相静止坐标系：
 *    α = a
 *    β = (a + 2b) / √3
 *
 *    或使用功率不变变换：
 *    α = (2/3) × (a - 0.5×b - 0.5×c)
 *    β = (2/3) × (√3/2) × (b - c)
 *
 * 2. Park变换 (αβ → dq坐标变换)
 *    将两相静止坐标系转换为两相旋转坐标系：
 *    d = α×cos(θ) + β×sin(θ)
 *    q = -α×sin(θ) + β×cos(θ)
 *
 *    其中θ为转子电角度
 *
 * 3. PI控制器
 *    在dq坐标系中进行解耦控制：
 *    - d轴控制磁通(弱磁控制)
 *    - q轴控制转矩
 *
 *    PI控制方程：
 *    u(k) = Kp×e(k) + Ki×∑e(k)×Ts
 *
 *    其中：
 *    - Kp: 比例增益
 *    - Ki: 积分增益
 *    - Ts: 采样周期
 *    - e(k): 当前误差
 *
 * 4. 反Park变换 (dq → αβ)
 *    α = d×cos(θ) - q×sin(θ)
 *    β = d×sin(θ) + q×cos(θ)
 *
 * 5. 空间矢量PWM (SVPWM)
 *    将αβ电压转换为三相PWM占空比：
 *
 *    扇区判断：
 *    sector = (β>0 ? 1:0) + (β/√3+α>0 ? 2:0) + (β/√3-α>0 ? 4:0)
 *
 *    占空比计算：
 *    T1 = Ts × √3 × (sin(π/3×sector) × β - cos(π/3×sector) × α) / Vdc
 *    T2 = Ts × √3 × (cos(π/3×(sector-1)) × α - sin(π/3×(sector-1)) × β) / Vdc
 *    T0 = Ts - T1 - T2
 *
 * 6. 无传感器控制
 *    基于反电动势观测器估算转子位置：
 *
 *    观测器方程：
 *    x̂₁(k+1) = x̂₁(k) + Ts×(u₁ - R×x̂₁(k))/L + G×(i₁ - x̂₁(k))
 *    x̂₂(k+1) = x̂₂(k) + Ts×(u₂ - R×x̂₂(k))/L + G×(i₂ - x̂₂(k))
 *
 *    角度估算：
 *    θ = atan2(ê₂, ê₁)
 *
 *    其中：
 *    - x̂₁, x̂₂: 观测的αβ轴电流
 *    - ê₁, ê₂: 估算的αβ轴反电动势
 *    - G: 观测器增益
 *    - R, L: 电机电阻和电感
 *
 * 7. 高频注入(HFI)
 *    低速无传感器控制技术，注入高频信号检测转子位置：
 *
 *    注入信号：
 *    v_hf = V_hf × cos(ωt)
 *
 *    响应信号处理：
 *    i_hf = I_hf × cos(ωt + φ)
 *
 *    位置信息提取：
 *    θ = φ/2 (对于凸极电机)
 *
 * 性能特点：
 * - 控制频率：20-30kHz
 * - 电流环响应：<1ms
 * - 速度环响应：10-100ms
 * - 位置精度：<0.1°
 * - 效率：>95%
 * - 噪音：<40dB
 */

#ifndef _GNU_SOURCE
#define _GNU_SOURCE
#endif

#include "mcpwm_foc.h"
#include "mc_interface.h"
#include "ch.h"
#include "hal.h"
#include "stm32f4xx_conf.h"
#include "digital_filter.h"
#include "utils_math.h"
#include "utils_sys.h"
#include "ledpwm.h"
#include "terminal.h"
//#include "encoder/encoder.h"
#include "encoder.h"
#include "commands.h"
#include "timeout.h"
#include "timer.h"
#include <math.h>
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include "virtual_motor.h"
#include "foc_math.h"

// Private variables
static volatile bool m_dccal_done = false;
static volatile float m_last_adc_isr_duration;
static volatile bool m_init_done = false;
static volatile motor_all_state_t m_motor_1;
#ifdef HW_HAS_DUAL_MOTORS
static volatile motor_all_state_t m_motor_2;
#endif
static volatile int m_isr_motor = 0;

// Private functions
static void control_current(motor_all_state_t *motor, float dt);
static void update_valpha_vbeta(motor_all_state_t *motor, float mod_alpha, float mod_beta);
static void stop_pwm_hw(motor_all_state_t *motor);
static void start_pwm_hw(motor_all_state_t *motor);
static void terminal_tmp(int argc, const char **argv);
static void terminal_plot_hfi(int argc, const char **argv);
static void timer_update(motor_all_state_t *motor, float dt);
static void input_current_offset_measurement( void );
static void hfi_update(volatile motor_all_state_t *motor, float dt);

// Threads
static THD_WORKING_AREA(timer_thread_wa, 512);
static THD_FUNCTION(timer_thread, arg);
static volatile bool timer_thd_stop;

static THD_WORKING_AREA(hfi_thread_wa, 512);
static THD_FUNCTION(hfi_thread, arg);
static volatile bool hfi_thd_stop;

static THD_WORKING_AREA(pid_thread_wa, 256);
static THD_FUNCTION(pid_thread, arg);
static volatile bool pid_thd_stop;

// Macros
#ifdef HW_HAS_3_SHUNTS
#define TIMER_UPDATE_DUTY_M1(duty1, duty2, duty3) \
		TIM1->CR1 |= TIM_CR1_UDIS; \
		TIM1->CCR1 = duty1; \
		TIM1->CCR2 = duty2; \
		TIM1->CCR3 = duty3; \
		TIM1->CR1 &= ~TIM_CR1_UDIS;

#define TIMER_UPDATE_DUTY_M2(duty1, duty2, duty3) \
		TIM8->CR1 |= TIM_CR1_UDIS; \
		TIM8->CCR1 = duty1; \
		TIM8->CCR2 = duty2; \
		TIM8->CCR3 = duty3; \
		TIM8->CR1 &= ~TIM_CR1_UDIS;
#else
#define TIMER_UPDATE_DUTY_M1(duty1, duty2, duty3) \
		TIM1->CR1 |= TIM_CR1_UDIS; \
		TIM1->CCR1 = duty1; \
		TIM1->CCR2 = duty3; \
		TIM1->CCR3 = duty2; \
		TIM1->CR1 &= ~TIM_CR1_UDIS;
#define TIMER_UPDATE_DUTY_M2(duty1, duty2, duty3) \
		TIM8->CR1 |= TIM_CR1_UDIS; \
		TIM8->CCR1 = duty1; \
		TIM8->CCR2 = duty3; \
		TIM8->CCR3 = duty2; \
		TIM8->CR1 &= ~TIM_CR1_UDIS;
#endif

#define TIMER_UPDATE_SAMP(samp) \
		TIM2->CCR2 = (samp / 2);

#define TIMER_UPDATE_SAMP_TOP_M1(samp, top) \
		TIM1->CR1 |= TIM_CR1_UDIS; \
		TIM2->CR1 |= TIM_CR1_UDIS; \
		TIM1->ARR = top; \
		TIM2->CCR2 = samp / 2; \
		TIM1->CR1 &= ~TIM_CR1_UDIS; \
		TIM2->CR1 &= ~TIM_CR1_UDIS;
#define TIMER_UPDATE_SAMP_TOP_M2(samp, top) \
		TIM8->CR1 |= TIM_CR1_UDIS; \
		TIM2->CR1 |= TIM_CR1_UDIS; \
		TIM8->ARR = top; \
		TIM2->CCR2 = samp / 2; \
		TIM8->CR1 &= ~TIM_CR1_UDIS; \
		TIM2->CR1 &= ~TIM_CR1_UDIS;

// #define M_MOTOR: For single motor compilation, expands to &m_motor_1.
// For dual motors, expands to &m_motor_1 or _2, depending on is_second_motor.
#ifdef HW_HAS_DUAL_MOTORS
#define M_MOTOR(is_second_motor) (is_second_motor ? &m_motor_2 : &m_motor_1)
#else
#define M_MOTOR(is_second_motor)  (((void)is_second_motor), &m_motor_1)
#endif

static void update_hfi_samples(foc_hfi_samples samples, volatile motor_all_state_t *motor) {
	utils_sys_lock_cnt();

	memset((void*)&motor->m_hfi, 0, sizeof(motor->m_hfi));
	switch (samples) {
	case HFI_SAMPLES_8:
		motor->m_hfi.samples = 8;
		motor->m_hfi.table_fact = 4;
		motor->m_hfi.fft_bin0_func = utils_fft8_bin0;
		motor->m_hfi.fft_bin1_func = utils_fft8_bin1;
		motor->m_hfi.fft_bin2_func = utils_fft8_bin2;
		break;

	case HFI_SAMPLES_16:
		motor->m_hfi.samples = 16;
		motor->m_hfi.table_fact = 2;
		motor->m_hfi.fft_bin0_func = utils_fft16_bin0;
		motor->m_hfi.fft_bin1_func = utils_fft16_bin1;
		motor->m_hfi.fft_bin2_func = utils_fft16_bin2;
		break;

	case HFI_SAMPLES_32:
		motor->m_hfi.samples = 32;
		motor->m_hfi.table_fact = 1;
		motor->m_hfi.fft_bin0_func = utils_fft32_bin0;
		motor->m_hfi.fft_bin1_func = utils_fft32_bin1;
		motor->m_hfi.fft_bin2_func = utils_fft32_bin2;
		break;
	}

	utils_sys_unlock_cnt();
}

static void timer_reinit(int f_zv) {
	utils_sys_lock_cnt();

	TIM_DeInit(TIM1);
	TIM_DeInit(TIM8);
	TIM_DeInit(TIM2);

	TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
	TIM_OCInitTypeDef TIM_OCInitStructure;
	TIM_BDTRInitTypeDef TIM_BDTRInitStructure;

	TIM1->CNT = 0;
	TIM2->CNT = 0;
	TIM8->CNT = 0;

	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM1, ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM8, ENABLE);

	TIM_TimeBaseStructure.TIM_Prescaler = 0;
	TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_CenterAligned1;
	TIM_TimeBaseStructure.TIM_Period = (SYSTEM_CORE_CLOCK / f_zv);
	TIM_TimeBaseStructure.TIM_ClockDivision = 0;
	TIM_TimeBaseStructure.TIM_RepetitionCounter = 0;

	TIM_TimeBaseInit(TIM1, &TIM_TimeBaseStructure);
	TIM_TimeBaseInit(TIM8, &TIM_TimeBaseStructure);

	TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;
	TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;
	TIM_OCInitStructure.TIM_OutputNState = TIM_OutputNState_Enable;
	TIM_OCInitStructure.TIM_Pulse = TIM1->ARR / 2;

#ifndef INVERTED_TOP_DRIVER_INPUT
	TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High; // gpio high = top fets on
#else
	TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_Low;
#endif
	TIM_OCInitStructure.TIM_OCIdleState = TIM_OCIdleState_Set;

#ifndef INVERTED_BOTTOM_DRIVER_INPUT
	TIM_OCInitStructure.TIM_OCNPolarity = TIM_OCNPolarity_High;  // gpio high = bottom fets on
#else
	TIM_OCInitStructure.TIM_OCNPolarity = TIM_OCNPolarity_Low;
#endif
	TIM_OCInitStructure.TIM_OCNIdleState = TIM_OCNIdleState_Set;

	TIM_OC1Init(TIM1, &TIM_OCInitStructure);
	TIM_OC2Init(TIM1, &TIM_OCInitStructure);
	TIM_OC3Init(TIM1, &TIM_OCInitStructure);
	TIM_OC4Init(TIM1, &TIM_OCInitStructure);

	TIM_OC1PreloadConfig(TIM1, TIM_OCPreload_Enable);
	TIM_OC2PreloadConfig(TIM1, TIM_OCPreload_Enable);
	TIM_OC3PreloadConfig(TIM1, TIM_OCPreload_Enable);
	TIM_OC4PreloadConfig(TIM1, TIM_OCPreload_Enable);

	TIM_OC1Init(TIM8, &TIM_OCInitStructure);
	TIM_OC2Init(TIM8, &TIM_OCInitStructure);
	TIM_OC3Init(TIM8, &TIM_OCInitStructure);
	TIM_OC4Init(TIM8, &TIM_OCInitStructure);

	TIM_OC1PreloadConfig(TIM8, TIM_OCPreload_Enable);
	TIM_OC2PreloadConfig(TIM8, TIM_OCPreload_Enable);
	TIM_OC3PreloadConfig(TIM8, TIM_OCPreload_Enable);
	TIM_OC4PreloadConfig(TIM8, TIM_OCPreload_Enable);

	// Automatic Output enable, Break, dead time and lock configuration
	TIM_BDTRInitStructure.TIM_OSSRState = TIM_OSSRState_Enable;
	TIM_BDTRInitStructure.TIM_OSSIState = TIM_OSSIState_Enable;
	TIM_BDTRInitStructure.TIM_LOCKLevel = TIM_LOCKLevel_OFF;
	TIM_BDTRInitStructure.TIM_DeadTime =  conf_general_calculate_deadtime(HW_DEAD_TIME_NSEC, SYSTEM_CORE_CLOCK);
	TIM_BDTRInitStructure.TIM_AutomaticOutput = TIM_AutomaticOutput_Disable;

#ifdef HW_USE_BRK
	// Enable BRK function. Hardware will asynchronously stop any PWM activity upon an
	// external fault signal. PWM outputs remain disabled until MCU is reset.
	// software will catch the BRK flag to report the fault code
	TIM_BDTRInitStructure.TIM_Break = TIM_Break_Enable;
	TIM_BDTRInitStructure.TIM_BreakPolarity = TIM_BreakPolarity_Low;
#else
	TIM_BDTRInitStructure.TIM_Break = TIM_Break_Disable;
	TIM_BDTRInitStructure.TIM_BreakPolarity = TIM_BreakPolarity_High;
#endif

	TIM_BDTRConfig(TIM1, &TIM_BDTRInitStructure);
	TIM_CCPreloadControl(TIM1, ENABLE);
	TIM_ARRPreloadConfig(TIM1, ENABLE);

	TIM_BDTRConfig(TIM8, &TIM_BDTRInitStructure);
	TIM_CCPreloadControl(TIM8, ENABLE);
	TIM_ARRPreloadConfig(TIM8, ENABLE);

	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);

	TIM_TimeBaseStructure.TIM_Prescaler = 0;
	TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
	TIM_TimeBaseStructure.TIM_Period = 0xFFFF;
	TIM_TimeBaseStructure.TIM_ClockDivision = 0;
	TIM_TimeBaseStructure.TIM_RepetitionCounter = 0;
	TIM_TimeBaseInit(TIM2, &TIM_TimeBaseStructure);

	TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;
	TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;
	TIM_OCInitStructure.TIM_Pulse = 250;
	TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High;
	TIM_OCInitStructure.TIM_OCNPolarity = TIM_OCNPolarity_High;
	TIM_OCInitStructure.TIM_OCIdleState = TIM_OCIdleState_Set;
	TIM_OCInitStructure.TIM_OCNIdleState = TIM_OCNIdleState_Set;
	TIM_OC1Init(TIM2, &TIM_OCInitStructure);
	TIM_OC1PreloadConfig(TIM2, TIM_OCPreload_Enable);
	TIM_OC2Init(TIM2, &TIM_OCInitStructure);
	TIM_OC2PreloadConfig(TIM2, TIM_OCPreload_Enable);
	TIM_OC3Init(TIM2, &TIM_OCInitStructure);
	TIM_OC3PreloadConfig(TIM2, TIM_OCPreload_Enable);

	TIM_ARRPreloadConfig(TIM2, ENABLE);
	TIM_CCPreloadControl(TIM2, ENABLE);

	// PWM outputs have to be enabled in order to trigger ADC on CCx
	TIM_CtrlPWMOutputs(TIM2, ENABLE);

#if defined HW_HAS_DUAL_MOTORS || defined HW_HAS_DUAL_PARALLEL
	// See: https://www.cnblogs.com/shangdawei/p/4758988.html
	TIM_SelectOutputTrigger(TIM1, TIM_TRGOSource_Enable);
	TIM_SelectMasterSlaveMode(TIM1, TIM_MasterSlaveMode_Enable);
	TIM_SelectInputTrigger(TIM8, TIM_TS_ITR0);
	TIM_SelectSlaveMode(TIM8, TIM_SlaveMode_Trigger);
	TIM_SelectOutputTrigger(TIM8, TIM_TRGOSource_Enable);
	TIM_SelectOutputTrigger(TIM8, TIM_TRGOSource_Update);
	TIM_SelectInputTrigger(TIM2, TIM_TS_ITR1);
	TIM_SelectSlaveMode(TIM2, TIM_SlaveMode_Reset);
#else
	TIM_SelectOutputTrigger(TIM1, TIM_TRGOSource_Update);
	TIM_SelectMasterSlaveMode(TIM1, TIM_MasterSlaveMode_Enable);
	TIM_SelectInputTrigger(TIM2, TIM_TS_ITR0);
	TIM_SelectSlaveMode(TIM2, TIM_SlaveMode_Reset);
#endif

#ifdef HW_HAS_DUAL_MOTORS
	TIM8->CNT = TIM1->ARR;
#else
	TIM8->CNT = 0;
#endif
	TIM1->CNT = 0;
	TIM_Cmd(TIM1, ENABLE);
	TIM_Cmd(TIM2, ENABLE);

	// Prevent all low side FETs from switching on
	stop_pwm_hw((motor_all_state_t*)&m_motor_1);
#ifdef HW_HAS_DUAL_MOTORS
	stop_pwm_hw((motor_all_state_t*)&m_motor_2);
#endif

	TIM_CtrlPWMOutputs(TIM1, ENABLE);
	TIM_CtrlPWMOutputs(TIM8, ENABLE);

	TIMER_UPDATE_SAMP(MCPWM_FOC_CURRENT_SAMP_OFFSET);

	// Enable CC2 interrupt, which will be fired in V0 and V7
	TIM_ITConfig(TIM2, TIM_IT_CC2, ENABLE);
	utils_sys_unlock_cnt();

	nvicEnableVector(TIM2_IRQn, 6);
}

void mcpwm_foc_init(mc_configuration *conf_m1, mc_configuration *conf_m2) {
	utils_sys_lock_cnt();

#ifndef HW_HAS_DUAL_MOTORS
	(void)conf_m2;
#endif

	m_init_done = false;

	memset((void*)&m_motor_1, 0, sizeof(motor_all_state_t));
	m_isr_motor = 0;

	m_motor_1.m_conf = conf_m1;
	m_motor_1.m_state = MC_STATE_OFF;
	m_motor_1.m_control_mode = CONTROL_MODE_NONE;
	m_motor_1.m_hall_dt_diff_last = 1.0;
	foc_precalc_values((motor_all_state_t*)&m_motor_1);
	update_hfi_samples(m_motor_1.m_conf->foc_hfi_samples, &m_motor_1);

#ifdef HW_HAS_DUAL_MOTORS
	memset((void*)&m_motor_2, 0, sizeof(motor_all_state_t));
	m_motor_2.m_conf = conf_m2;
	m_motor_2.m_state = MC_STATE_OFF;
	m_motor_2.m_control_mode = CONTROL_MODE_NONE;
	m_motor_2.m_hall_dt_diff_last = 1.0;
	foc_precalc_values((motor_all_state_t*)&m_motor_2);
	update_hfi_samples(m_motor_2.m_conf->foc_hfi_samples, &m_motor_2);
#endif

	virtual_motor_init(conf_m1);

	TIM_DeInit(TIM1);
	TIM_DeInit(TIM2);
	TIM_DeInit(TIM8);
	TIM1->CNT = 0;
	TIM2->CNT = 0;
	TIM8->CNT = 0;

	ADC_CommonInitTypeDef ADC_CommonInitStructure;
	DMA_InitTypeDef DMA_InitStructure;
	ADC_InitTypeDef ADC_InitStructure;

	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA2 | RCC_AHB1Periph_GPIOA | RCC_AHB1Periph_GPIOC, ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC1 | RCC_APB2Periph_ADC2 | RCC_APB2Periph_ADC3, ENABLE);

	dmaStreamAllocate(STM32_DMA_STREAM(STM32_DMA_STREAM_ID(2, 4)),
					  5,
					  (stm32_dmaisr_t)mcpwm_foc_adc_int_handler,
					  (void *)0);

	DMA_InitStructure.DMA_Channel = DMA_Channel_0;
	DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)&ADC_Value;
	DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&ADC->CDR;
	DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralToMemory;
	DMA_InitStructure.DMA_BufferSize = HW_ADC_CHANNELS;
	DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
	DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
	DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;
	DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;
	DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;
	DMA_InitStructure.DMA_Priority = DMA_Priority_High;
	DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;
	DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_1QuarterFull;
	DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;
	DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;
	DMA_Init(DMA2_Stream4, &DMA_InitStructure);

	DMA_Cmd(DMA2_Stream4, ENABLE);

	// Note: The half transfer interrupt is used as we already have all current and voltage
	// samples by then and we can start processing them. Entering the interrupt earlier gives
	// more cycles to finish it and update the timer before the next zero vector. This helps
	// at higher f_zv. Only use this if the three first samples are current samples.
#if ADC_IND_CURR1 < 3 && ADC_IND_CURR2 < 3 && ADC_IND_CURR3 < 3
	DMA_ITConfig(DMA2_Stream4, DMA_IT_HT, ENABLE);
#else
	DMA_ITConfig(DMA2_Stream4, DMA_IT_TC, ENABLE);
#endif

	// Note that the ADC is running at 42MHz, which is higher than the
	// specified 36MHz in the data sheet, but it works.
	ADC_CommonInitStructure.ADC_Mode = ADC_TripleMode_RegSimult;
	ADC_CommonInitStructure.ADC_Prescaler = ADC_Prescaler_Div2;
	ADC_CommonInitStructure.ADC_DMAAccessMode = ADC_DMAAccessMode_1;
	ADC_CommonInitStructure.ADC_TwoSamplingDelay = ADC_TwoSamplingDelay_5Cycles;
	ADC_CommonInit(&ADC_CommonInitStructure);

	ADC_InitStructure.ADC_Resolution = ADC_Resolution_12b;
	ADC_InitStructure.ADC_ScanConvMode = ENABLE;
	ADC_InitStructure.ADC_ContinuousConvMode = DISABLE;
	ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_Falling;
	ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T2_CC2;
	ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;
	ADC_InitStructure.ADC_NbrOfConversion = HW_ADC_NBR_CONV;

	ADC_Init(ADC1, &ADC_InitStructure);
	ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_None;
	ADC_InitStructure.ADC_ExternalTrigConv = 0;
	ADC_Init(ADC2, &ADC_InitStructure);
	ADC_Init(ADC3, &ADC_InitStructure);

	ADC_TempSensorVrefintCmd(ENABLE);
	ADC_MultiModeDMARequestAfterLastTransferCmd(ENABLE);

	hw_setup_adc_channels();

	ADC_Cmd(ADC1, ENABLE);
	ADC_Cmd(ADC2, ENABLE);
	ADC_Cmd(ADC3, ENABLE);

	timer_reinit((int)m_motor_1.m_conf->foc_f_zv);

	stop_pwm_hw((motor_all_state_t*)&m_motor_1);
#ifdef HW_HAS_DUAL_MOTORS
	stop_pwm_hw((motor_all_state_t*)&m_motor_2);
#endif

	TIMER_UPDATE_SAMP(MCPWM_FOC_CURRENT_SAMP_OFFSET);

	// Enable CC2 interrupt, which will be fired in V0 and V7
	TIM_ITConfig(TIM2, TIM_IT_CC2, ENABLE);
	nvicEnableVector(TIM2_IRQn, 6);

	utils_sys_unlock_cnt();

	CURRENT_FILTER_ON();
	CURRENT_FILTER_ON_M2();
	ENABLE_GATE();
	DCCAL_OFF();

	if (m_motor_1.m_conf->foc_offsets_cal_on_boot) {
		systime_t cal_start_time = chVTGetSystemTimeX();
		float cal_start_timeout = 10.0;

		// Wait for input voltage to rise above minimum voltage
		while (mc_interface_get_input_voltage_filtered() < m_motor_1.m_conf->l_min_vin) {
			chThdSleepMilliseconds(1);
			if (UTILS_AGE_S(cal_start_time) >= cal_start_timeout) {
				m_dccal_done = true;
				break;
			}
		}

		// Wait for input voltage to settle
		if (!m_dccal_done) {
			float v_in_last = mc_interface_get_input_voltage_filtered();
			systime_t v_in_stable_time = chVTGetSystemTimeX();
			while (UTILS_AGE_S(v_in_stable_time) < 2.0) {
				chThdSleepMilliseconds(1);

				float v_in_now = mc_interface_get_input_voltage_filtered();
				if (fabsf(v_in_now - v_in_last) > 1.5) {
					v_in_last = v_in_now;
					v_in_stable_time = chVTGetSystemTimeX();
				}

				if (UTILS_AGE_S(cal_start_time) >= cal_start_timeout) {
					m_dccal_done = true;
					break;
				}
			}
		}

		// Wait for fault codes to go away
		if (!m_dccal_done) {
			while (mc_interface_get_fault() != FAULT_CODE_NONE) {
				chThdSleepMilliseconds(1);
				if (UTILS_AGE_S(cal_start_time) >= cal_start_timeout) {
					m_dccal_done = true;
					break;
				}
			}
		}

		if (!m_dccal_done) {
			m_motor_1.m_conf->foc_offsets_voltage[0] = MCCONF_FOC_OFFSETS_VOLTAGE_0;
			m_motor_1.m_conf->foc_offsets_voltage[1] = MCCONF_FOC_OFFSETS_VOLTAGE_1;
			m_motor_1.m_conf->foc_offsets_voltage[2] = MCCONF_FOC_OFFSETS_VOLTAGE_2;

			m_motor_1.m_conf->foc_offsets_voltage_undriven[0] = MCCONF_FOC_OFFSETS_VOLTAGE_UNDRIVEN_0;
			m_motor_1.m_conf->foc_offsets_voltage_undriven[1] = MCCONF_FOC_OFFSETS_VOLTAGE_UNDRIVEN_1;
			m_motor_1.m_conf->foc_offsets_voltage_undriven[2] = MCCONF_FOC_OFFSETS_VOLTAGE_UNDRIVEN_2;

			m_motor_1.m_conf->foc_offsets_current[0] = MCCONF_FOC_OFFSETS_CURRENT_0;
			m_motor_1.m_conf->foc_offsets_current[1] = MCCONF_FOC_OFFSETS_CURRENT_1;
			m_motor_1.m_conf->foc_offsets_current[2] = MCCONF_FOC_OFFSETS_CURRENT_2;

#ifdef HW_HAS_DUAL_MOTORS
			m_motor_2.m_conf->foc_offsets_voltage[0] = MCCONF_FOC_OFFSETS_VOLTAGE_0;
			m_motor_2.m_conf->foc_offsets_voltage[1] = MCCONF_FOC_OFFSETS_VOLTAGE_1;
			m_motor_2.m_conf->foc_offsets_voltage[2] = MCCONF_FOC_OFFSETS_VOLTAGE_2;

			m_motor_2.m_conf->foc_offsets_voltage_undriven[0] = MCCONF_FOC_OFFSETS_VOLTAGE_UNDRIVEN_0;
			m_motor_2.m_conf->foc_offsets_voltage_undriven[1] = MCCONF_FOC_OFFSETS_VOLTAGE_UNDRIVEN_1;
			m_motor_2.m_conf->foc_offsets_voltage_undriven[2] = MCCONF_FOC_OFFSETS_VOLTAGE_UNDRIVEN_2;

			m_motor_2.m_conf->foc_offsets_current[0] = MCCONF_FOC_OFFSETS_CURRENT_0;
			m_motor_2.m_conf->foc_offsets_current[1] = MCCONF_FOC_OFFSETS_CURRENT_1;
			m_motor_2.m_conf->foc_offsets_current[2] = MCCONF_FOC_OFFSETS_CURRENT_2;
#endif

			mcpwm_foc_dc_cal(false);
		}
	} else {
		m_dccal_done = true;
	}

	// Start threads
	timer_thd_stop = false;
	chThdCreateStatic(timer_thread_wa, sizeof(timer_thread_wa), NORMALPRIO, timer_thread, NULL);

	hfi_thd_stop = false;
	chThdCreateStatic(hfi_thread_wa, sizeof(hfi_thread_wa), NORMALPRIO, hfi_thread, NULL);

	pid_thd_stop = false;
	chThdCreateStatic(pid_thread_wa, sizeof(pid_thread_wa), NORMALPRIO, pid_thread, NULL);

	// Check if the system has resumed from IWDG reset and generate fault if it has. This can be used to
	// tell if some frozen thread caused a watchdog reset. Note that this also will trigger after running
	// the bootloader and after the reset command.
	if (timeout_had_IWDG_reset()) {
		mc_interface_fault_stop(FAULT_CODE_BOOTING_FROM_WATCHDOG_RESET, false, false);
	}

	terminal_register_command_callback(
			"foc_tmp",
			"FOC Test Print",
			0,
			terminal_tmp);

	terminal_register_command_callback(
			"foc_plot_hfi_en",
			"Enable HFI plotting. 0: off, 1: DFT, 2: Raw",
			"[en]",
			terminal_plot_hfi);

	m_init_done = true;
}

void mcpwm_foc_deinit(void) {
	if (!m_init_done) {
		return;
	}

	m_init_done = false;

	timer_thd_stop = true;
	while (timer_thd_stop) {
		chThdSleepMilliseconds(1);
	}

	hfi_thd_stop = true;
	while (hfi_thd_stop) {
		chThdSleepMilliseconds(1);
	}

	pid_thd_stop = true;
	while (pid_thd_stop) {
		chThdSleepMilliseconds(1);
	}

	TIM_DeInit(TIM1);
	TIM_DeInit(TIM2);
	TIM_DeInit(TIM8);
	ADC_DeInit();
	DMA_DeInit(DMA2_Stream4);
	nvicDisableVector(ADC_IRQn);
	dmaStreamRelease(STM32_DMA_STREAM(STM32_DMA_STREAM_ID(2, 4)));
}

static volatile motor_all_state_t *get_motor_now(void) {
#ifdef HW_HAS_DUAL_MOTORS
	return mc_interface_motor_now() == 1 ? &m_motor_1 : &m_motor_2;
#else
	return &m_motor_1;
#endif
}

bool mcpwm_foc_init_done(void) {
	return m_init_done;
}

void mcpwm_foc_set_configuration(mc_configuration *configuration) {
	get_motor_now()->m_conf = configuration;
	foc_precalc_values((motor_all_state_t*)get_motor_now());

	// Below we check if anything in the configuration changed that requires stopping the motor.

	uint32_t top = SYSTEM_CORE_CLOCK / (int)configuration->foc_f_zv;
	if (TIM1->ARR != top) {
#ifdef HW_HAS_DUAL_MOTORS
		m_motor_1.m_control_mode = CONTROL_MODE_NONE;
		m_motor_1.m_state = MC_STATE_OFF;
		stop_pwm_hw((motor_all_state_t*)&m_motor_1);

		m_motor_2.m_control_mode = CONTROL_MODE_NONE;
		m_motor_2.m_state = MC_STATE_OFF;
		stop_pwm_hw((motor_all_state_t*)&m_motor_2);

		timer_reinit((int)configuration->foc_f_zv);
#else
		get_motor_now()->m_control_mode = CONTROL_MODE_NONE;
		get_motor_now()->m_state = MC_STATE_OFF;
		stop_pwm_hw((motor_all_state_t*)get_motor_now());
		TIMER_UPDATE_SAMP_TOP_M1(MCPWM_FOC_CURRENT_SAMP_OFFSET, top);
#ifdef  HW_HAS_DUAL_PARALLEL
		TIMER_UPDATE_SAMP_TOP_M2(MCPWM_FOC_CURRENT_SAMP_OFFSET, top);
#endif
#endif
	}

	if (((1 << get_motor_now()->m_conf->foc_hfi_samples) * 8) != get_motor_now()->m_hfi.samples) {
		get_motor_now()->m_control_mode = CONTROL_MODE_NONE;
		get_motor_now()->m_state = MC_STATE_OFF;
		stop_pwm_hw((motor_all_state_t*)get_motor_now());
		update_hfi_samples(get_motor_now()->m_conf->foc_hfi_samples, get_motor_now());
	}

	virtual_motor_set_configuration(configuration);
}

mc_state mcpwm_foc_get_state(void) {
	return get_motor_now()->m_state;
}

mc_control_mode mcpwm_foc_control_mode(void) {
	return get_motor_now()->m_control_mode;
}

bool mcpwm_foc_is_dccal_done(void) {
	return m_dccal_done;
}

/**
 * Get the current motor used in the mcpwm ISR
 *
 * @return
 * 0: Not in ISR
 * 1: Motor 1
 * 2: Motor 2
 */
int mcpwm_foc_isr_motor(void) {
	return m_isr_motor;
}

/**
 * Switch off all FETs.
 */
void mcpwm_foc_stop_pwm(bool is_second_motor) {
	motor_all_state_t *motor = (motor_all_state_t*)M_MOTOR(is_second_motor);

	motor->m_control_mode = CONTROL_MODE_NONE;
	motor->m_state = MC_STATE_OFF;
	stop_pwm_hw(motor);
}

/**
 * Use duty cycle control. Absolute values less than MCPWM_MIN_DUTY_CYCLE will
 * stop the motor.
 *
 * @param dutyCycle
 * The duty cycle to use
 */
void mcpwm_foc_set_duty(float dutyCycle) {
	get_motor_now()->m_control_mode = CONTROL_MODE_DUTY;
	get_motor_now()->m_duty_cycle_set = dutyCycle;

	if (get_motor_now()->m_state != MC_STATE_RUNNING) {
		get_motor_now()->m_motor_released = false;
		get_motor_now()->m_state = MC_STATE_RUNNING;
	}
}

/**
 * Use duty cycle control. Absolute values less than MCPWM_MIN_DUTY_CYCLE will
 * stop the motor.
 *
 * WARNING: This function does not use ramping. A too large step with a large motor
 * can destroy hardware.
 *
 * @param dutyCycle
 * The duty cycle to use.
 */
void mcpwm_foc_set_duty_noramp(float dutyCycle) {
	// TODO: Actually do this without ramping
	mcpwm_foc_set_duty(dutyCycle);
}

/**
 * Use PID rpm control. Note that this value has to be multiplied by half of
 * the number of motor poles.
 *
 * @param rpm
 * The electrical RPM goal value to use.
 */
void mcpwm_foc_set_pid_speed(float rpm) {
	volatile motor_all_state_t *motor = get_motor_now();

	if (motor->m_conf->s_pid_ramp_erpms_s > 0.0 ) {
		if (motor->m_control_mode != CONTROL_MODE_SPEED ||
				motor->m_state != MC_STATE_RUNNING) {
			motor->m_speed_pid_set_rpm = mcpwm_foc_get_rpm();
		}

		motor->m_speed_command_rpm = rpm;
	} else {
		motor->m_speed_pid_set_rpm = rpm;
	}

	motor->m_control_mode = CONTROL_MODE_SPEED;

	if (motor->m_state != MC_STATE_RUNNING &&
			fabsf(rpm) >= motor->m_conf->s_pid_min_erpm) {
		motor->m_motor_released = false;
		motor->m_state = MC_STATE_RUNNING;
	}
}

/**
 * Use PID position control. Note that this only works when encoder support
 * is enabled.
 *
 * @param pos
 * The desired position of the motor in degrees.

 */
void mcpwm_foc_set_pid_pos(float pos) {
	get_motor_now()->m_control_mode = CONTROL_MODE_POS;
	get_motor_now()->m_pos_pid_set = pos;

	if (get_motor_now()->m_state != MC_STATE_RUNNING) {
		get_motor_now()->m_motor_released = false;
		get_motor_now()->m_state = MC_STATE_RUNNING;
	}
}

/**
 * Use current control and specify a goal current to use. The sign determines
 * the direction of the torque. Absolute values less than
 * conf->cc_min_current will release the motor.
 *
 * @param current
 * The current to use.
 */
void mcpwm_foc_set_current(float current) {
	get_motor_now()->m_control_mode = CONTROL_MODE_CURRENT;
	get_motor_now()->m_iq_set = current;
	get_motor_now()->m_id_set = 0;
	
	if (fabsf(current) < get_motor_now()->m_conf->cc_min_current) {
		return;
	}

	if (get_motor_now()->m_state != MC_STATE_RUNNING) {
		get_motor_now()->m_motor_released = false;
		get_motor_now()->m_state = MC_STATE_RUNNING;
	}
}

void mcpwm_foc_release_motor(void) {
	get_motor_now()->m_control_mode = CONTROL_MODE_CURRENT;
	get_motor_now()->m_iq_set = 0.0;
	get_motor_now()->m_id_set = 0.0;
	get_motor_now()->m_motor_released = true;
}

/**
 * Brake the motor with a desired current. Absolute values less than
 * conf->cc_min_current will release the motor.
 *
 * @param current
 * The current to use. Positive and negative values give the same effect.
 */
void mcpwm_foc_set_brake_current(float current) {
	get_motor_now()->m_control_mode = CONTROL_MODE_CURRENT_BRAKE;
	get_motor_now()->m_iq_set = current;

	if (fabsf(current) < get_motor_now()->m_conf->cc_min_current) {
		return;
	}

	if (get_motor_now()->m_state != MC_STATE_RUNNING) {
		get_motor_now()->m_motor_released = false;
		get_motor_now()->m_state = MC_STATE_RUNNING;
	}
}

/**
 * Apply a fixed static current vector in open loop to emulate an electric
 * handbrake.
 *
 * @param current
 * The brake current to use.
 */
void mcpwm_foc_set_handbrake(float current) {
	get_motor_now()->m_control_mode = CONTROL_MODE_HANDBRAKE;
	get_motor_now()->m_iq_set = current;

	if (fabsf(current) < get_motor_now()->m_conf->cc_min_current) {
		return;
	}

	if (get_motor_now()->m_state != MC_STATE_RUNNING) {
		get_motor_now()->m_motor_released = false;
		get_motor_now()->m_state = MC_STATE_RUNNING;
	}
}

/**
 * Produce an openloop rotating current.
 *
 * @param current
 * The current to use.
 *
 * @param rpm
 * The RPM to use.
 *
 */
void mcpwm_foc_set_openloop_current(float current, float rpm) {
	utils_truncate_number(&current, -get_motor_now()->m_conf->l_current_max * get_motor_now()->m_conf->l_current_max_scale,
						  get_motor_now()->m_conf->l_current_max * get_motor_now()->m_conf->l_current_max_scale);

	get_motor_now()->m_control_mode = CONTROL_MODE_OPENLOOP;
	get_motor_now()->m_iq_set = current;
	get_motor_now()->m_openloop_speed = RPM2RADPS_f(rpm);

	if (fabsf(current) < get_motor_now()->m_conf->cc_min_current) {
		return;
	}

	if (get_motor_now()->m_state != MC_STATE_RUNNING) {
		get_motor_now()->m_motor_released = false;
		get_motor_now()->m_state = MC_STATE_RUNNING;
	}
}

/**
 * Produce an openloop current at a fixed phase.
 *
 * @param current
 * The current to use.
 *
 * @param phase
 * The phase to use in degrees, range [0.0 360.0]
 */
void mcpwm_foc_set_openloop_phase(float current, float phase) {
	utils_truncate_number(&current, -get_motor_now()->m_conf->l_current_max * get_motor_now()->m_conf->l_current_max_scale,
						  get_motor_now()->m_conf->l_current_max * get_motor_now()->m_conf->l_current_max_scale);

	get_motor_now()->m_control_mode = CONTROL_MODE_OPENLOOP_PHASE;
	get_motor_now()->m_id_set = current;
	get_motor_now()->m_iq_set = 0;

	get_motor_now()->m_openloop_phase = DEG2RAD_f(phase);
	utils_norm_angle_rad((float*)&get_motor_now()->m_openloop_phase);

	if (fabsf(current) < get_motor_now()->m_conf->cc_min_current) {
		return;
	}

	if (get_motor_now()->m_state != MC_STATE_RUNNING) {
		get_motor_now()->m_motor_released = false;
		get_motor_now()->m_state = MC_STATE_RUNNING;
	}
}

/**
 * Get current offsets,
 * this is used by the virtual motor to save the current offsets,
 * when it is connected
 */
void mcpwm_foc_get_current_offsets(
		volatile float *curr0_offset,
		volatile float *curr1_offset,
		volatile float *curr2_offset,
		bool is_second_motor) {
	volatile motor_all_state_t *motor = M_MOTOR(is_second_motor);
	*curr0_offset = motor->m_conf->foc_offsets_current[0];
	*curr1_offset = motor->m_conf->foc_offsets_current[1];
	*curr2_offset = motor->m_conf->foc_offsets_current[2];
}

/**
 * Set current offsets values,
 * this is used by the virtual motor to set the previously saved offsets back,
 * when it is disconnected
 */
void mcpwm_foc_set_current_offsets(volatile float curr0_offset,
								   volatile float curr1_offset,
								   volatile float curr2_offset) {
	get_motor_now()->m_conf->foc_offsets_current[0] = curr0_offset;
	get_motor_now()->m_conf->foc_offsets_current[1] = curr1_offset;
	get_motor_now()->m_conf->foc_offsets_current[2] = curr2_offset;
}

void mcpwm_foc_get_voltage_offsets(
		float *v0_offset,
		float *v1_offset,
		float *v2_offset,
		bool is_second_motor) {
	volatile motor_all_state_t *motor = M_MOTOR(is_second_motor);
	*v0_offset = motor->m_conf->foc_offsets_voltage[0];
	*v1_offset = motor->m_conf->foc_offsets_voltage[1];
	*v2_offset = motor->m_conf->foc_offsets_voltage[2];
}

void mcpwm_foc_get_voltage_offsets_undriven(
		float *v0_offset,
		float *v1_offset,
		float *v2_offset,
		bool is_second_motor) {
	volatile motor_all_state_t *motor = M_MOTOR(is_second_motor);
	*v0_offset = motor->m_conf->foc_offsets_voltage_undriven[0];
	*v1_offset = motor->m_conf->foc_offsets_voltage_undriven[1];
	*v2_offset = motor->m_conf->foc_offsets_voltage_undriven[2];
}

void mcpwm_foc_get_currents_adc(
		float *ph0,
		float *ph1,
		float *ph2,
		bool is_second_motor) {
	volatile motor_all_state_t *motor = M_MOTOR(is_second_motor);
	*ph0 = motor->m_currents_adc[0];
	*ph1 = motor->m_currents_adc[1];
	*ph2 = motor->m_currents_adc[2];
}

/**
 * Produce an openloop rotating voltage.
 *
 * @param dutyCycle
 * The duty cycle to use.
 *
 * @param rpm
 * The RPM to use.
 */
void mcpwm_foc_set_openloop_duty(float dutyCycle, float rpm) {
	get_motor_now()->m_control_mode = CONTROL_MODE_OPENLOOP_DUTY;
	get_motor_now()->m_duty_cycle_set = dutyCycle;
	get_motor_now()->m_openloop_speed = RPM2RADPS_f(rpm);

	if (get_motor_now()->m_state != MC_STATE_RUNNING) {
		get_motor_now()->m_motor_released = false;
		get_motor_now()->m_state = MC_STATE_RUNNING;
	}
}

/**
 * Produce an openloop voltage at a fixed phase.
 *
 * @param dutyCycle
 * The duty cycle to use.
 *
 * @param phase
 * The phase to use in degrees, range [0.0 360.0]
 */
void mcpwm_foc_set_openloop_duty_phase(float dutyCycle, float phase) {
	get_motor_now()->m_control_mode = CONTROL_MODE_OPENLOOP_DUTY_PHASE;
	get_motor_now()->m_duty_cycle_set = dutyCycle;
	get_motor_now()->m_openloop_phase = DEG2RAD_f(phase);
	utils_norm_angle_rad((float*)&get_motor_now()->m_openloop_phase);

	if (get_motor_now()->m_state != MC_STATE_RUNNING) {
		get_motor_now()->m_motor_released = false;
		get_motor_now()->m_state = MC_STATE_RUNNING;
	}
}

float mcpwm_foc_get_duty_cycle_set(void) {
	return get_motor_now()->m_duty_cycle_set;
}

float mcpwm_foc_get_duty_cycle_now(void) {
	return get_motor_now()->m_motor_state.duty_now;
}

float mcpwm_foc_get_pid_pos_set(void) {
	return get_motor_now()->m_pos_pid_set;
}

float mcpwm_foc_get_pid_pos_now(void) {
	return get_motor_now()->m_pos_pid_now;
}

/**
 * Get the current switching frequency.
 *
 * @return
 * The switching frequency in Hz.
 */
float mcpwm_foc_get_switching_frequency_now(void) {
	return get_motor_now()->m_conf->foc_f_zv;
}

/**
 * Get the current sampling frequency.
 *
 * @return
 * The sampling frequency in Hz.
 */
float mcpwm_foc_get_sampling_frequency_now(void) {
#ifdef HW_HAS_PHASE_SHUNTS
	if (get_motor_now()->m_conf->foc_sample_v0_v7) {
		return get_motor_now()->m_conf->foc_f_zv;
	} else {
		return get_motor_now()->m_conf->foc_f_zv / 2.0;
	}
#else
	return get_motor_now()->m_conf->foc_f_zv / 2.0;
#endif
}

/**
 * Returns Ts used for virtual motor sync
 */
float mcpwm_foc_get_ts(void) {
#ifdef HW_HAS_PHASE_SHUNTS
	if (get_motor_now()->m_conf->foc_sample_v0_v7) {
		return (1.0 / get_motor_now()->m_conf->foc_f_zv) ;
	} else {
		return (1.0 / (get_motor_now()->m_conf->foc_f_zv / 2.0));
	}
#else
	return (1.0 / get_motor_now()->m_conf->foc_f_zv) ;
#endif
}

bool mcpwm_foc_is_using_encoder(void) {
	return get_motor_now()->m_using_encoder;
}

void mcpwm_foc_get_observer_state(float *x1, float *x2) {
	volatile motor_all_state_t *motor = get_motor_now();
	*x1 = motor->m_observer_state.x1;
	*x2 = motor->m_observer_state.x2;
}

/**
 * Set current off delay. Prevent the current controller from switching off modulation
 * for target currents < cc_min_current for this amount of time.
 */
void mcpwm_foc_set_current_off_delay(float delay_sec) {
	if (get_motor_now()->m_current_off_delay < delay_sec) {
		get_motor_now()->m_current_off_delay = delay_sec;
	}
}

float mcpwm_foc_get_tot_current_motor(bool is_second_motor) {
	volatile motor_all_state_t *motor = M_MOTOR(is_second_motor);
	return SIGN(motor->m_motor_state.vq * motor->m_motor_state.iq) * motor->m_motor_state.i_abs;
}

float mcpwm_foc_get_tot_current_filtered_motor(bool is_second_motor) {
	volatile motor_all_state_t *motor = M_MOTOR(is_second_motor);
	return SIGN(motor->m_motor_state.vq * motor->m_motor_state.iq_filter) * motor->m_motor_state.i_abs_filter;
}

float mcpwm_foc_get_tot_current_in_motor(bool is_second_motor) {
	return M_MOTOR(is_second_motor)->m_motor_state.i_bus;
}

float mcpwm_foc_get_tot_current_in_filtered_motor(bool is_second_motor) {
	// TODO: Filter current?
	return M_MOTOR(is_second_motor)->m_motor_state.i_bus;
}

float mcpwm_foc_get_abs_motor_current_motor(bool is_second_motor) {
	return M_MOTOR(is_second_motor)->m_motor_state.i_abs;
}

float mcpwm_foc_get_abs_motor_current_filtered_motor(bool is_second_motor) {
	return M_MOTOR(is_second_motor)->m_motor_state.i_abs_filter;
}

mc_state mcpwm_foc_get_state_motor(bool is_second_motor) {
	return M_MOTOR(is_second_motor)->m_state;
}

/**
 * Calculate the current RPM of the motor. This is a signed value and the sign
 * depends on the direction the motor is rotating in. Note that this value has
 * to be divided by half the number of motor poles.
 *
 * @return
 * The RPM value.
 */
float mcpwm_foc_get_rpm(void) {
	return RADPS2RPM_f(get_motor_now()->m_motor_state.speed_rad_s);
	//	return get_motor_now()->m_speed_est_fast * RADPS2RPM_f;
}

/**
 * Same as above, but uses the fast and noisier estimator.
 */
float mcpwm_foc_get_rpm_fast(void) {
	return RADPS2RPM_f(get_motor_now()->m_speed_est_fast);
}

/**
 * Same as above, but uses the faster and noisier estimator.
 */
float mcpwm_foc_get_rpm_faster(void) {
	return RADPS2RPM_f(get_motor_now()->m_speed_est_faster);
}

/**
 * Get the motor current. The sign of this value will
 * represent whether the motor is drawing (positive) or generating
 * (negative) current. This is the q-axis current which produces torque.
 *
 * @return
 * The motor current.
 */
float mcpwm_foc_get_tot_current(void) {
	volatile motor_all_state_t *motor = get_motor_now();
	return SIGN(motor->m_motor_state.vq * motor->m_motor_state.iq) * motor->m_motor_state.i_abs;
}

/**
 * Get the filtered motor current. The sign of this value will
 * represent whether the motor is drawing (positive) or generating
 * (negative) current. This is the q-axis current which produces torque.
 *
 * @return
 * The filtered motor current.
 */
float mcpwm_foc_get_tot_current_filtered(void) {
	volatile motor_all_state_t *motor = get_motor_now();
	return SIGN(motor->m_motor_state.vq * motor->m_motor_state.iq_filter) * motor->m_motor_state.i_abs_filter;
}

/**
 * Get the magnitude of the motor current, which includes both the
 * D and Q axis.
 *
 * @return
 * The magnitude of the motor current.
 */
float mcpwm_foc_get_abs_motor_current(void) {
	return get_motor_now()->m_motor_state.i_abs;
}

/**
 * Get the magnitude of the motor current unbalance
 *
 * @return
 * The magnitude of the phase currents unbalance.
 */
float mcpwm_foc_get_abs_motor_current_unbalance(void) {
	return get_motor_now()->m_curr_unbalance * FAC_CURRENT;
}

/**
 * Get the magnitude of the motor voltage.
 *
 * @return
 * The magnitude of the motor voltage.
 */
float mcpwm_foc_get_abs_motor_voltage(void) {
	const float vd_tmp = get_motor_now()->m_motor_state.vd;
	const float vq_tmp = get_motor_now()->m_motor_state.vq;
	return NORM2_f(vd_tmp, vq_tmp);
}

/**
 * Get the filtered magnitude of the motor current, which includes both the
 * D and Q axis.
 *
 * @return
 * The magnitude of the motor current.
 */
float mcpwm_foc_get_abs_motor_current_filtered(void) {
	return get_motor_now()->m_motor_state.i_abs_filter;
}

/**
 * Get the motor current. The sign of this value represents the direction
 * in which the motor generates torque.
 *
 * @return
 * The motor current.
 */
float mcpwm_foc_get_tot_current_directional(void) {
	return get_motor_now()->m_motor_state.iq;
}

/**
 * Get the filtered motor current. The sign of this value represents the
 * direction in which the motor generates torque.
 *
 * @return
 * The filtered motor current.
 */
float mcpwm_foc_get_tot_current_directional_filtered(void) {
	return get_motor_now()->m_motor_state.iq_filter;
}

/**
 * Get the direct axis motor current.
 *
 * @return
 * The D axis current.
 */
float mcpwm_foc_get_id(void) {
	return get_motor_now()->m_motor_state.id;
}

/**
 * Get the quadrature axis motor current.
 *
 * @return
 * The Q axis current.
 */
float mcpwm_foc_get_iq(void) {
	return get_motor_now()->m_motor_state.iq;
}

/**
 * Get the filtered direct axis motor current.
 *
 * @return
 * The D axis current.
 */
float mcpwm_foc_get_id_filter(void) {
	return get_motor_now()->m_motor_state.id_filter;
}

/**
 * Get the filtered quadrature axis motor current.
 *
 * @return
 * The Q axis current.
 */
float mcpwm_foc_get_iq_filter(void) {
	return get_motor_now()->m_motor_state.iq_filter;
}

/**
 * Get the input current to the motor controller.
 *
 * @return
 * The input current.
 */
float mcpwm_foc_get_tot_current_in(void) {
	return get_motor_now()->m_motor_state.i_bus;
}

/**
 * Get the filtered input current to the motor controller.
 *
 * @return
 * The filtered input current.
 */
float mcpwm_foc_get_tot_current_in_filtered(void) {
	return get_motor_now()->m_motor_state.i_bus; // TODO: Calculate filtered current?
}

/**
 * Set the number of steps the motor has rotated. This number is signed and
 * becomes a negative when the motor is rotating backwards.
 *
 * @param steps
 * New number of steps will be set after this call.
 *
 * @return
 * The previous tachometer value in motor steps. The number of motor revolutions will
 * be this number divided by (3 * MOTOR_POLE_NUMBER).
 */
int mcpwm_foc_set_tachometer_value(int steps) {
	int val = get_motor_now()->m_tachometer;
	get_motor_now()->m_tachometer = steps;
	return val;
}

/**
 * Read the number of steps the motor has rotated. This number is signed and
 * will return a negative number when the motor is rotating backwards.
 *
 * @param reset
 * If true, the tachometer counter will be reset after this call.
 *
 * @return
 * The tachometer value in motor steps. The number of motor revolutions will
 * be this number divided by (3 * MOTOR_POLE_NUMBER).
 */
int mcpwm_foc_get_tachometer_value(bool reset) {
	int val = get_motor_now()->m_tachometer;

	if (reset) {
		get_motor_now()->m_tachometer = 0;
	}

	return val;
}

/**
 * Read the absolute number of steps the motor has rotated.
 *
 * @param reset
 * If true, the tachometer counter will be reset after this call.
 *
 * @return
 * The tachometer value in motor steps. The number of motor revolutions will
 * be this number divided by (3 * MOTOR_POLE_NUMBER).
 */
int mcpwm_foc_get_tachometer_abs_value(bool reset) {
	int val = get_motor_now()->m_tachometer_abs;

	if (reset) {
		get_motor_now()->m_tachometer_abs = 0;
	}

	return val;
}

/**
 * Read the motor phase.
 *
 * @return
 * The phase angle in degrees.
 */
float mcpwm_foc_get_phase(void) {
	float angle = RAD2DEG_f(get_motor_now()->m_motor_state.phase);
	utils_norm_angle(&angle);
	return angle;
}

/**
 * Read the phase that the observer has calculated.
 *
 * @return
 * The phase angle in degrees.
 */
float mcpwm_foc_get_phase_observer(void) {
	float angle = RAD2DEG_f(get_motor_now()->m_phase_now_observer);
	utils_norm_angle(&angle);
	return angle;
}

/**
 * Read the phase from based on the encoder.
 *
 * @return
 * The phase angle in degrees.
 */
float mcpwm_foc_get_phase_encoder(void) {
	float angle = RAD2DEG_f(get_motor_now()->m_phase_now_encoder);
	utils_norm_angle(&angle);
	return angle;
}

float mcpwm_foc_get_vd(void) {
	return get_motor_now()->m_motor_state.vd;
}

float mcpwm_foc_get_vq(void) {
	return get_motor_now()->m_motor_state.vq;
}

float mcpwm_foc_get_mod_alpha_raw(void) {
	return get_motor_now()->m_motor_state.mod_alpha_raw;
}

float mcpwm_foc_get_mod_beta_raw(void) {
	return get_motor_now()->m_motor_state.mod_beta_raw;
}

float mcpwm_foc_get_mod_alpha_measured(void) {
	return get_motor_now()->m_motor_state.mod_alpha_measured;
}

float mcpwm_foc_get_mod_beta_measured(void) {
	return get_motor_now()->m_motor_state.mod_beta_measured;
}

float mcpwm_foc_get_est_lambda(void) {
	return get_motor_now()->m_observer_state.lambda_est;
}

float mcpwm_foc_get_est_res(void) {
	return get_motor_now()->m_res_est;
}

// NOTE: Requires the regular HFI sensor mode to run
float mcpwm_foc_get_est_ind(void) {
	float real_bin0, imag_bin0;
	get_motor_now()->m_hfi.fft_bin0_func((float*)get_motor_now()->m_hfi.buffer, &real_bin0, &imag_bin0);
	return real_bin0;
}

/**
 * Measure encoder offset and direction.
 *
 * @param current
 * The locking open loop current for the motor.
 *
 * @param print
 * Controls logging during the detection procedure. Set to true to enable
 * logging.
 *
 * @param offset
 * The detected offset.
 *
 * @param ratio
 * The ratio between electrical and mechanical revolutions
 *
 * @param direction
 * The detected direction.
 *
 * @param inverted
 * Is set to true if the encoder reports an increase in angle in the opposite
 * direction of the motor.
 *
 * @return
 * The fault code
 */
int mcpwm_foc_encoder_detect(float current, bool print, float *offset, float *ratio, bool *inverted) {
	int fault = FAULT_CODE_NONE;
	mc_interface_lock();

	volatile motor_all_state_t *motor = get_motor_now();

	motor->m_phase_override = true;
	motor->m_id_set = current;
	motor->m_iq_set = 0.0;
	motor->m_control_mode = CONTROL_MODE_CURRENT;
	motor->m_motor_released = false;
	motor->m_state = MC_STATE_RUNNING;

	// Disable timeout
	systime_t tout = timeout_get_timeout_msec();
	float tout_c = timeout_get_brake_current();
	KILL_SW_MODE tout_ksw = timeout_get_kill_sw_mode();
	timeout_reset();
	timeout_configure(60000, 0.0, KILL_SW_MODE_DISABLED);

	// Save configuration
	float offset_old = motor->m_conf->foc_encoder_offset;
	float inverted_old = motor->m_conf->foc_encoder_inverted;
	float ratio_old = motor->m_conf->foc_encoder_ratio;
	float ldiff_old = motor->m_conf->foc_motor_ld_lq_diff;

	motor->m_conf->foc_encoder_offset = 0.0;
	motor->m_conf->foc_encoder_inverted = false;
	motor->m_conf->foc_encoder_ratio = 1.0;
	motor->m_conf->foc_motor_ld_lq_diff = 0.0;

	// Find index
	int cnt = 0;
	while(!encoder_index_found()) {
		for (float i = 0.0;i < 2.0 * M_PI;i += (2.0 * M_PI) / 500.0) {
			motor->m_phase_now_override = i;
			fault = mc_interface_get_fault();
			if (fault != FAULT_CODE_NONE) {
				goto exit_encoder_detect;
			}
			chThdSleepMilliseconds(1);
		}

		cnt++;
		if (cnt > 30) {
			// Give up
			break;
		}
	}

	if (print) {
		commands_printf("Index found");
	}

	// Rotate
	for (float i = 0.0;i < 2.0 * M_PI;i += (2.0 * M_PI) / 500.0) {
		motor->m_phase_now_override = i;
		fault = mc_interface_get_fault();
		if (fault != FAULT_CODE_NONE) {
			goto exit_encoder_detect;
		}
		chThdSleepMilliseconds(1);
	}

	if (print) {
		commands_printf("Rotated for sync");
	}

	// Inverted and ratio
	chThdSleepMilliseconds(1000);

	const int it_rat = 30;
	float s_sum = 0.0;
	float c_sum = 0.0;
	float first = motor->m_phase_now_encoder;

	for (int i = 0; i < it_rat; i++) {
		float phase_old = motor->m_phase_now_encoder;
		float phase_ovr_tmp = motor->m_phase_now_override;
		for (float j = phase_ovr_tmp; j < phase_ovr_tmp + (2.0 / 3.0) * M_PI;
			 j += (2.0 * M_PI) / 500.0) {
			motor->m_phase_now_override = j;
			fault = mc_interface_get_fault();
			if (fault != FAULT_CODE_NONE) {
				goto exit_encoder_detect;
			}
			chThdSleepMilliseconds(1);
		}

		utils_norm_angle_rad((float*)&motor->m_phase_now_override);
		chThdSleepMilliseconds(300);
		timeout_reset();
		float diff = utils_angle_difference_rad(motor->m_phase_now_encoder, phase_old);

		float s, c;
		sincosf(diff, &s, &c);
		s_sum += s;
		c_sum += c;

		if (print) {
			commands_printf("Diff: %.2f", (double)RAD2DEG_f(diff));
		}

		if (i > 3 && fabsf(utils_angle_difference_rad(motor->m_phase_now_encoder, first)) < fabsf(diff / 2.0)) {
			break;
		}
	}

	first = motor->m_phase_now_encoder;

	for (int i = 0; i < it_rat; i++) {
		float phase_old = motor->m_phase_now_encoder;
		float phase_ovr_tmp = motor->m_phase_now_override;
		for (float j = phase_ovr_tmp; j > phase_ovr_tmp - (2.0 / 3.0) * M_PI; j -= (2.0 * M_PI) / 500.0) {
			motor->m_phase_now_override = j;
			fault = mc_interface_get_fault();
			if (fault != FAULT_CODE_NONE) {
				goto exit_encoder_detect;
			}
			chThdSleepMilliseconds(1);
		}
		utils_norm_angle_rad((float*)&motor->m_phase_now_override);
		chThdSleepMilliseconds(300);
		timeout_reset();
		float diff = utils_angle_difference_rad(phase_old, motor->m_phase_now_encoder);

		float s, c;
		sincosf(diff, &s, &c);
		s_sum += s;
		c_sum += c;

		if (print) {
			commands_printf("Diff: %.2f", (double)RAD2DEG_f(diff));
		}

		if (i > 3 && fabsf(utils_angle_difference_rad(motor->m_phase_now_encoder, first)) < fabsf(diff / 2.0)) {
			break;
		}
	}

	float diff = RAD2DEG_f(atan2f(s_sum, c_sum));
	*inverted = diff < 0.0;
	*ratio = roundf(((2.0 / 3.0) * 180.0) / fabsf(diff));

	motor->m_conf->foc_encoder_inverted = *inverted;
	motor->m_conf->foc_encoder_ratio = *ratio;

	if (print) {
		commands_printf("Inversion and ratio detected");
		commands_printf("Ratio: %.2f", (double)*ratio);
	}

	// Rotate
	for (float i = motor->m_phase_now_override;i < 2.0 * M_PI;i += (2.0 * M_PI) / 500.0) {
		motor->m_phase_now_override = i;
		fault = mc_interface_get_fault();
		if (fault != FAULT_CODE_NONE) {
			goto exit_encoder_detect;
		}
		chThdSleepMilliseconds(2);
	}

	if (print) {
		commands_printf("Rotated for sync");
		commands_printf("Enc: %.2f", (double)encoder_read_deg());
	}

	const int it_ofs = motor->m_conf->foc_encoder_ratio * 3.0;
	s_sum = 0.0;
	c_sum = 0.0;

	for (int i = 0;i < it_ofs;i++) {
		float step = (2.0 * M_PI * motor->m_conf->foc_encoder_ratio) / ((float)it_ofs);
		float override = (float)i * step;

		while (motor->m_phase_now_override != override) {
			utils_step_towards((float*)&motor->m_phase_now_override, override, step / 100.0);
			fault = mc_interface_get_fault();
			if (fault != FAULT_CODE_NONE) {
				goto exit_encoder_detect;
			}
			chThdSleepMilliseconds(4);
		}

		chThdSleepMilliseconds(100);
		timeout_reset();

		float angle_diff = utils_angle_difference_rad(motor->m_phase_now_encoder, motor->m_phase_now_override);
		float s, c;
		sincosf(angle_diff, &s, &c);
		s_sum += s;
		c_sum += c;

		if (print) {
			commands_printf("Ovr: %.2f/%.2f Diff: %.2f", (double)override, (double)(it_ofs * step), (double)RAD2DEG_f(angle_diff));
		}
	}

	for (int i = it_ofs;i > 0;i--) {
		float step = (2.0 * M_PI * motor->m_conf->foc_encoder_ratio) / ((float)it_ofs);
		float override = (float)i * step;

		while (motor->m_phase_now_override != override) {
			utils_step_towards((float*)&motor->m_phase_now_override, override, step / 100.0);
			fault = mc_interface_get_fault();
			if (fault != FAULT_CODE_NONE) {
				goto exit_encoder_detect;
			}
			chThdSleepMilliseconds(4);
		}

		chThdSleepMilliseconds(100);
		timeout_reset();

		float angle_diff = utils_angle_difference_rad(motor->m_phase_now_encoder, motor->m_phase_now_override);
		float s, c;
		sincosf(angle_diff, &s, &c);
		s_sum += s;
		c_sum += c;

		if (print) {
			commands_printf("Ovr: %.2f/%.2f Diff: %.2f", (double)override, (double)(it_ofs * step), (double)RAD2DEG_f(angle_diff));
		}
	}

	*offset = RAD2DEG_f(atan2f(s_sum, c_sum));

	if (print) {
		commands_printf("Avg: %.2f", (double)*offset);
	}

	utils_norm_angle(offset);

	if (print) {
		commands_printf("Offset detected");
	}

	exit_encoder_detect:
	motor->m_id_set = 0.0;
	motor->m_iq_set = 0.0;
	motor->m_phase_override = false;
	motor->m_control_mode = CONTROL_MODE_NONE;
	motor->m_state = MC_STATE_OFF;
	stop_pwm_hw((motor_all_state_t*)motor);

	// Restore configuration
	motor->m_conf->foc_encoder_inverted = inverted_old;
	motor->m_conf->foc_encoder_offset = offset_old;
	motor->m_conf->foc_encoder_ratio = ratio_old;
	motor->m_conf->foc_motor_ld_lq_diff = ldiff_old;

	// Enable timeout
	timeout_configure(tout, tout_c, tout_ksw);

	mc_interface_unlock();
	return fault;
}

/**
 * Lock the motor with a current and sample the voltage and current to
 * calculate the motor resistance.
 *
 * @param current
 * The locking current.
 *
 * @param samples
 * The number of samples to take.
 *
 * @param stop_after
 * Stop motor after finishing the measurement. Otherwise, the current will
 * still be applied after returning. Setting this to false is useful if you want
 * to run this function again right away, without stopping the motor in between.
 *
 * @param resistance
 * The calculated motor resistance
 *
 * @return
 * The fault code.
 */
int mcpwm_foc_measure_resistance(float current, int samples, bool stop_after, float *resistance) {
	mc_interface_lock();

	volatile motor_all_state_t *motor = get_motor_now();
	int fault = FAULT_CODE_NONE;

	motor->m_phase_override = true;
	motor->m_phase_now_override = 0.0;
	motor->m_id_set = 0.0;
	motor->m_control_mode = CONTROL_MODE_CURRENT;
	motor->m_motor_released = false;
	motor->m_state = MC_STATE_RUNNING;

	// Disable timeout
	systime_t tout = timeout_get_timeout_msec();
	float tout_c = timeout_get_brake_current();
	KILL_SW_MODE tout_ksw = timeout_get_kill_sw_mode();
	timeout_reset();
	timeout_configure(60000, 0.0, KILL_SW_MODE_DISABLED);

	// Ramp up the current slowly
	while (fabsf(motor->m_iq_set - current) > 0.001) {
		utils_step_towards((float*)&motor->m_iq_set, current, fabsf(current) / 200.0);
		fault = mc_interface_get_fault();
		if (fault != FAULT_CODE_NONE) {
			motor->m_id_set = 0.0;
			motor->m_iq_set = 0.0;
			motor->m_phase_override = false;
			motor->m_control_mode = CONTROL_MODE_NONE;
			motor->m_state = MC_STATE_OFF;
			stop_pwm_hw((motor_all_state_t*)motor);

			timeout_configure(tout, tout_c, tout_ksw);
			mc_interface_unlock();

			return fault;
		}
		chThdSleepMilliseconds(1);
	}

	// Wait for the current to rise and the motor to lock.
	chThdSleepMilliseconds(50);

	// Sample
	motor->m_samples.avg_current_tot = 0.0;
	motor->m_samples.avg_voltage_tot = 0.0;
	motor->m_samples.sample_num = 0;

	int cnt = 0;
	while (motor->m_samples.sample_num < samples) {
		chThdSleepMilliseconds(1);
		cnt++;
		// Timeout
		if (cnt > 10000) {
			break;
		}
		fault = mc_interface_get_fault();
		if (fault != FAULT_CODE_NONE) {
			motor->m_id_set = 0.0;
			motor->m_iq_set = 0.0;
			motor->m_phase_override = false;
			motor->m_control_mode = CONTROL_MODE_NONE;
			motor->m_state = MC_STATE_OFF;
			stop_pwm_hw((motor_all_state_t*)motor);

			timeout_configure(tout, tout_c, tout_ksw);
			mc_interface_unlock();

			return fault;
		}
	}

	const float current_avg = motor->m_samples.avg_current_tot / (float)motor->m_samples.sample_num;
	const float voltage_avg = motor->m_samples.avg_voltage_tot / (float)motor->m_samples.sample_num;

	// Stop
	if (stop_after) {
		motor->m_id_set = 0.0;
		motor->m_iq_set = 0.0;
		motor->m_phase_override = false;
		motor->m_control_mode = CONTROL_MODE_NONE;
		motor->m_state = MC_STATE_OFF;
		stop_pwm_hw((motor_all_state_t*)motor);
	}

	// Enable timeout
	timeout_configure(tout, tout_c, tout_ksw);
	mc_interface_unlock();

	*resistance = voltage_avg / current_avg;

	return fault;
}

/**
 * Measure the motor inductance with short voltage pulses.
 *
 * @param duty
 * The duty cycle to use in the pulses.
 *
 * @param samples
 * The number of samples to average over.
 *
 * @param
 * The current that was used for this measurement.
 *
 * @inductance
 * The average d and q axis inductance in uH.
 *
 * @return
 * The fault code
 */
int mcpwm_foc_measure_inductance(float duty, int samples, float *curr, float *ld_lq_diff, float *inductance) {
	volatile motor_all_state_t *motor = get_motor_now();
	int fault = FAULT_CODE_NONE;

	mc_foc_sensor_mode sensor_mode_old = motor->m_conf->foc_sensor_mode;
	float f_zv_old = motor->m_conf->foc_f_zv;
	float hfi_voltage_start_old = motor->m_conf->foc_hfi_voltage_start;
	float hfi_voltage_run_old = motor->m_conf->foc_hfi_voltage_run;
	float hfi_voltage_max_old = motor->m_conf->foc_hfi_voltage_max;
	float sl_erpm_hfi_old = motor->m_conf->foc_sl_erpm_hfi;
	bool sample_v0_v7_old = motor->m_conf->foc_sample_v0_v7;
	foc_hfi_samples samples_old = motor->m_conf->foc_hfi_samples;
	bool sample_high_current_old = motor->m_conf->foc_sample_high_current;

	mc_interface_lock();
	motor->m_control_mode = CONTROL_MODE_NONE;
	motor->m_state = MC_STATE_OFF;
	stop_pwm_hw((motor_all_state_t*)motor);

	motor->m_conf->foc_sensor_mode = FOC_SENSOR_MODE_HFI;
	motor->m_conf->foc_hfi_voltage_start = duty * mc_interface_get_input_voltage_filtered() * (2.0 / 3.0) * SQRT3_BY_2;
	motor->m_conf->foc_hfi_voltage_run = duty * mc_interface_get_input_voltage_filtered() * (2.0 / 3.0) * SQRT3_BY_2;
	motor->m_conf->foc_hfi_voltage_max = duty * mc_interface_get_input_voltage_filtered() * (2.0 / 3.0) * SQRT3_BY_2;
	motor->m_conf->foc_sl_erpm_hfi = 20000.0;
	motor->m_conf->foc_sample_v0_v7 = false;
	motor->m_conf->foc_hfi_samples = HFI_SAMPLES_32;
	motor->m_conf->foc_sample_high_current = false;

	if (motor->m_conf->foc_f_zv > 30.0e3) {
		motor->m_conf->foc_f_zv = 30.0e3;
	}

	mcpwm_foc_set_configuration(motor->m_conf);

	chThdSleepMilliseconds(1);

	timeout_reset();
	mcpwm_foc_set_duty(0.0);
	chThdSleepMilliseconds(1);

	int ready_cnt = 0;
	while (!motor->m_hfi.ready) {
		chThdSleepMilliseconds(1);
		ready_cnt++;
		if (ready_cnt > 100) {
			break;
		}
	}

	if (samples < 10) {
		samples = 10;
	}

	float l_sum = 0.0;
	float ld_lq_diff_sum = 0.0;
	float i_sum = 0.0;
	float iterations = 0.0;

	for (int i = 0;i < (samples / 10);i++) {
		timeout_reset();
		mcpwm_foc_set_duty(0.0);

		fault = mc_interface_get_fault();
		if (fault != FAULT_CODE_NONE) {
			motor->m_id_set = 0.0;
			motor->m_iq_set = 0.0;
			motor->m_control_mode = CONTROL_MODE_NONE;
			motor->m_state = MC_STATE_OFF;
			stop_pwm_hw((motor_all_state_t*)motor);

			motor->m_conf->foc_sensor_mode = sensor_mode_old;
			motor->m_conf->foc_f_zv = f_zv_old;
			motor->m_conf->foc_hfi_voltage_start = hfi_voltage_start_old;
			motor->m_conf->foc_hfi_voltage_run = hfi_voltage_run_old;
			motor->m_conf->foc_hfi_voltage_max = hfi_voltage_max_old;
			motor->m_conf->foc_sl_erpm_hfi = sl_erpm_hfi_old;
			motor->m_conf->foc_sample_v0_v7 = sample_v0_v7_old;
			motor->m_conf->foc_hfi_samples = samples_old;
			motor->m_conf->foc_sample_high_current = sample_high_current_old;

			mcpwm_foc_set_configuration(motor->m_conf);

			mc_interface_unlock();

			return fault;
		}

		chThdSleepMilliseconds(10);

		float real_bin0, imag_bin0;
		float real_bin2, imag_bin2;
		float real_bin0_i, imag_bin0_i;

		motor->m_hfi.fft_bin0_func((float*)motor->m_hfi.buffer, &real_bin0, &imag_bin0);
		motor->m_hfi.fft_bin2_func((float*)motor->m_hfi.buffer, &real_bin2, &imag_bin2);
		motor->m_hfi.fft_bin0_func((float*)motor->m_hfi.buffer_current, &real_bin0_i, &imag_bin0_i);

		l_sum += real_bin0;
		i_sum += real_bin0_i;

		// See https://vesc-project.com/comment/8338#comment-8338
		ld_lq_diff_sum += 4.0 * NORM2_f(real_bin2, imag_bin2);

		iterations++;
	}

	mcpwm_foc_set_current(0.0);

	motor->m_conf->foc_sensor_mode = sensor_mode_old;
	motor->m_conf->foc_f_zv = f_zv_old;
	motor->m_conf->foc_hfi_voltage_start = hfi_voltage_start_old;
	motor->m_conf->foc_hfi_voltage_run = hfi_voltage_run_old;
	motor->m_conf->foc_hfi_voltage_max = hfi_voltage_max_old;
	motor->m_conf->foc_sl_erpm_hfi = sl_erpm_hfi_old;
	motor->m_conf->foc_sample_v0_v7 = sample_v0_v7_old;
	motor->m_conf->foc_hfi_samples = samples_old;
	motor->m_conf->foc_sample_high_current = sample_high_current_old;

	mcpwm_foc_set_configuration(motor->m_conf);

	mc_interface_unlock();

	// The observer is more stable when the inductance is underestimated compared to overestimated,
	// so scale it by 0.8. This helps motors that start to saturate at higher currents and when
	// the hardware has problems measuring the inductance correctly. Another reason for decreasing the
	// measured value is that delays in the hardware and/or a high resistance compared to inductance
	// will cause the value to be overestimated.
	// NOTE: This used to be 0.8, but was changed to 0.9 as that works better with HFIv2 on most motors.
	float ind_scale_factor = 0.9;

	if (curr) {
		*curr = i_sum / iterations;
	}

	if (ld_lq_diff) {
		*ld_lq_diff = (ld_lq_diff_sum / iterations) * 1e6 * ind_scale_factor;
	}

	*inductance = (l_sum / iterations) * 1e6 * ind_scale_factor;
	return fault;
}

/**
 * Measure the motor inductance with short voltage pulses. The difference from the
 * other function is that this one will aim for a specific measurement current. It
 * will also use an appropriate switching frequency.
 *
 * @param curr_goal
 * The measurement current to aim for.
 *
 * @param samples
 * The number of samples to average over.
 *
 * @param *curr
 * The current that was used for this measurement.
 *
 * @inductance
 * The average d and q axis inductance in uH.
 *
 * @return
 * The fault code
 */
int mcpwm_foc_measure_inductance_current(float curr_goal, int samples, float *curr, float *ld_lq_diff, float *inductance) {
	int fault = FAULT_CODE_NONE;
	float duty_last = 0.0;
	for (float i = 0.02;i < 0.5;i *= 1.5) {
		utils_truncate_number_abs(&i, 0.6);
		float i_tmp;
		fault = mcpwm_foc_measure_inductance(i, 10, &i_tmp, 0, 0);
		if (fault != FAULT_CODE_NONE) {
			return fault;
		}

		duty_last = i;
		if (i_tmp >= curr_goal) {
			break;
		}
	}
	fault = mcpwm_foc_measure_inductance(duty_last, samples, curr, ld_lq_diff, inductance);
	return fault;
}

bool mcpwm_foc_beep(float freq, float time, float voltage) {
	volatile motor_all_state_t *motor = get_motor_now();

	mc_foc_sensor_mode sensor_mode_old = motor->m_conf->foc_sensor_mode;
	float f_zv_old = motor->m_conf->foc_f_zv;
	float hfi_voltage_start_old = motor->m_conf->foc_hfi_voltage_start;
	float hfi_voltage_run_old = motor->m_conf->foc_hfi_voltage_run;
	float hfi_voltage_max_old = motor->m_conf->foc_hfi_voltage_max;
	float sl_erpm_hfi_old = motor->m_conf->foc_sl_erpm_hfi;
	bool sample_v0_v7_old = motor->m_conf->foc_sample_v0_v7;
	foc_hfi_samples samples_old = motor->m_conf->foc_hfi_samples;
	uint16_t start_samples_old = motor->m_conf->foc_hfi_start_samples;

	mc_interface_lock();
	motor->m_control_mode = CONTROL_MODE_NONE;
	motor->m_state = MC_STATE_OFF;
	stop_pwm_hw((motor_all_state_t*)motor);

	motor->m_conf->foc_sensor_mode = FOC_SENSOR_MODE_HFI;
	motor->m_conf->foc_hfi_voltage_start = voltage;
	motor->m_conf->foc_hfi_voltage_run = voltage;
	motor->m_conf->foc_hfi_voltage_max = voltage;
	motor->m_conf->foc_sl_erpm_hfi = 20000.0;
	motor->m_conf->foc_sample_v0_v7 = false;
	motor->m_conf->foc_hfi_samples = HFI_SAMPLES_8;
	motor->m_conf->foc_hfi_start_samples = 10;

	freq *= 4.0;

	if (freq > 3500) {
		motor->m_conf->foc_sensor_mode = FOC_SENSOR_MODE_HFI_V3;
		freq /= 8.0;
	}

	motor->m_conf->foc_f_zv = freq * 8.0;

	utils_truncate_number(&motor->m_conf->foc_f_zv, 3.0e3, 30.0e3);

	mcpwm_foc_set_configuration(motor->m_conf);

	chThdSleepMilliseconds(1);

	timeout_reset();
	mcpwm_foc_set_duty(0.0);

	int ms_sleep = (time * 1000.0) - 1;
	if (ms_sleep > 0) {
		chThdSleepMilliseconds(ms_sleep);
	}

	mcpwm_foc_set_current(0.0);

	motor->m_conf->foc_sensor_mode = sensor_mode_old;
	motor->m_conf->foc_f_zv = f_zv_old;
	motor->m_conf->foc_hfi_voltage_start = hfi_voltage_start_old;
	motor->m_conf->foc_hfi_voltage_run = hfi_voltage_run_old;
	motor->m_conf->foc_hfi_voltage_max = hfi_voltage_max_old;
	motor->m_conf->foc_sl_erpm_hfi = sl_erpm_hfi_old;
	motor->m_conf->foc_sample_v0_v7 = sample_v0_v7_old;
	motor->m_conf->foc_hfi_samples = samples_old;
	motor->m_conf->foc_hfi_start_samples = start_samples_old;

	mcpwm_foc_set_configuration(motor->m_conf);

	mc_interface_unlock();

	return true;
}

/**
 * Automatically measure the resistance and inductance of the motor with small steps.
 *
 * @param res
 * The measured resistance in ohm.
 *
 * @param ind
 * The measured inductance in microhenry.
 *
 * @param ld_lq_diff
 * The measured difference in D axis and Q axis inductance.
 *
 * @return
 * The fault code
 */
int mcpwm_foc_measure_res_ind(float *res, float *ind, float *ld_lq_diff) {
	volatile motor_all_state_t *motor = get_motor_now();
	int fault = FAULT_CODE_NONE;

	const float kp_old = motor->m_conf->foc_current_kp;
	const float ki_old = motor->m_conf->foc_current_ki;
	const float res_old = motor->m_conf->foc_motor_r;

	motor->m_conf->foc_current_kp = 0.001;
	motor->m_conf->foc_current_ki = 1.0;

	float i_last = 0.0;
	for (float i = 2.0;i < (motor->m_conf->l_current_max / 2.0);i *= 1.5) {
		float r_tmp = 0.0;
		fault = mcpwm_foc_measure_resistance(i, 20, false, &r_tmp);
		if (fault != FAULT_CODE_NONE || r_tmp == 0.0) {
			goto exit_measure_res_ind;
		}
		if (i > (1.0 / r_tmp)) {
			i_last = i;
			break;
		}
	}

	if (i_last < 0.01) {
		i_last = (motor->m_conf->l_current_max / 2.0);
	}

#ifdef HW_AXIOM_FORCE_HIGH_CURRENT_MEASUREMENTS
	i_last = (motor->m_conf->l_current_max / 2.0);
#endif

	fault = mcpwm_foc_measure_resistance(i_last, 200, true, res);
	if (fault == FAULT_CODE_NONE && *res != 0.0) {
		motor->m_conf->foc_motor_r = *res;
		mcpwm_foc_set_current(0.0);
		chThdSleepMilliseconds(10);
		fault = mcpwm_foc_measure_inductance_current(i_last, 200, 0, ld_lq_diff, ind);
	}

	exit_measure_res_ind:
	motor->m_conf->foc_current_kp = kp_old;
	motor->m_conf->foc_current_ki = ki_old;
	motor->m_conf->foc_motor_r = res_old;
	return fault;
}

/**
 * Run the motor in open loop and figure out at which angles the hall sensors are.
 *
 * @param current
 * Current to use.
 *
 * @param hall_table
 * Table to store the result to.
 *
 * @result
 * true: Success
 * false: Something went wrong
 *
 * @return
 * The fault code
 */
int mcpwm_foc_hall_detect(float current, uint8_t *hall_table, bool *result) {
	volatile motor_all_state_t *motor = get_motor_now();
	int fault = FAULT_CODE_NONE;
	mc_interface_lock();

	motor->m_phase_override = true;
	motor->m_id_set = 0.0;
	motor->m_iq_set = 0.0;
	motor->m_control_mode = CONTROL_MODE_CURRENT;
	motor->m_motor_released = false;
	motor->m_state = MC_STATE_RUNNING;

	// MTPA overrides id target
	MTPA_MODE mtpa_old = motor->m_conf->foc_mtpa_mode;
	motor->m_conf->foc_mtpa_mode = MTPA_MODE_OFF;

	// Disable timeout
	systime_t tout = timeout_get_timeout_msec();
	float tout_c = timeout_get_brake_current();
	KILL_SW_MODE tout_ksw = timeout_get_kill_sw_mode();
	timeout_reset();
	timeout_configure(60000, 0.0, KILL_SW_MODE_DISABLED);

	// Lock the motor
	motor->m_phase_now_override = 0;

	*result = false;

	for (int i = 0;i < 1000;i++) {
		motor->m_id_set = (float)i * current / 1000.0;
		fault = mc_interface_get_fault();
		if (fault != FAULT_CODE_NONE) {
			goto exit_hall_detect;
		}
		chThdSleepMilliseconds(1);
	}

	float sin_hall[8];
	float cos_hall[8];
	int hall_iterations[8];
	memset(sin_hall, 0, sizeof(sin_hall));
	memset(cos_hall, 0, sizeof(cos_hall));
	memset(hall_iterations, 0, sizeof(hall_iterations));

	// Forwards
	for (int i = 0;i < 3;i++) {
		for (int j = 0;j < 360;j++) {
			motor->m_phase_now_override = DEG2RAD_f(j);
			fault = mc_interface_get_fault();
			if (fault != FAULT_CODE_NONE) {
				goto exit_hall_detect;
			}
			chThdSleepMilliseconds(5);

			int hall = utils_read_hall(motor != &m_motor_1, motor->m_conf->m_hall_extra_samples);
			float s, c;
			sincosf(motor->m_phase_now_override, &s, &c);
			sin_hall[hall] += s;
			cos_hall[hall] += c;
			hall_iterations[hall]++;
		}
	}

	// Reverse
	for (int i = 0;i < 3;i++) {
		for (int j = 360;j >= 0;j--) {
			motor->m_phase_now_override = DEG2RAD_f(j);
			fault = mc_interface_get_fault();
			if (fault != FAULT_CODE_NONE) {
				goto exit_hall_detect;
			}
			chThdSleepMilliseconds(5);

			int hall = utils_read_hall(motor != &m_motor_1, motor->m_conf->m_hall_extra_samples);
			float s, c;
			sincosf(motor->m_phase_now_override, &s, &c);
			sin_hall[hall] += s;
			cos_hall[hall] += c;
			hall_iterations[hall]++;
		}
	}

	int fails = 0;
	for(int i = 0;i < 8;i++) {
		if (hall_iterations[i] > 30) {
			float ang = RAD2DEG_f(atan2f(sin_hall[i], cos_hall[i]));
			utils_norm_angle(&ang);
			hall_table[i] = (uint8_t)(ang * 200.0 / 360.0);
		} else {
			hall_table[i] = 255;
			fails++;
		}
	}
	*result = (fails == 2);

	exit_hall_detect:
	motor->m_id_set = 0.0;
	motor->m_iq_set = 0.0;
	motor->m_phase_override = false;
	motor->m_control_mode = CONTROL_MODE_NONE;
	motor->m_state = MC_STATE_OFF;
	stop_pwm_hw((motor_all_state_t*)motor);
	motor->m_conf->foc_mtpa_mode = mtpa_old;
	timeout_configure(tout, tout_c, tout_ksw);
	mc_interface_unlock();

	return fault;
}

/**
 * Calibrate voltage and current offsets. For the observer to work at low modulation it
 * is very important to get all current and voltage offsets right. Therefore we store
 * the offsets for when the motor is undriven and when it is driven separately. The
 * motor is driven at 50% modulation on all phases when measuring the driven offset, which
 * corresponds to space-vector modulation with 0 amplitude.
 *
 * cal_undriven:
 * Calibrate undriven voltages too. This requires the motor to stand still.
 *
 * return:
 * -1: Timed out while waiting for fault code to go away.
 * 1: Success
 *
 */
int mcpwm_foc_dc_cal(bool cal_undriven) {
	// Wait max 5 seconds for DRV-fault to go away
	int cnt = 0;
	while(IS_DRV_FAULT()){
		chThdSleepMilliseconds(1);
		cnt++;
		if (cnt > 5000) {
			return -1;
		}
	};

	chThdSleepMilliseconds(1000);

	// Disable timeout
	systime_t tout = timeout_get_timeout_msec();
	float tout_c = timeout_get_brake_current();
	KILL_SW_MODE tout_ksw = timeout_get_kill_sw_mode();
	timeout_reset();
	timeout_configure(60000, 0.0, KILL_SW_MODE_DISABLED);

	// Measure driven offsets

	const float samples = 1000.0;
	float current_sum[3] = {0.0, 0.0, 0.0};
	float voltage_sum[3] = {0.0, 0.0, 0.0};

	TIMER_UPDATE_DUTY_M1(TIM1->ARR / 2, TIM1->ARR / 2, TIM1->ARR / 2);

	// Start PWM on phase 1
	stop_pwm_hw((motor_all_state_t*)&m_motor_1);
	PHASE_FILTER_ON();
	TIM_SelectOCxM(TIM1, TIM_Channel_1, TIM_OCMode_PWM1);
	TIM_CCxCmd(TIM1, TIM_Channel_1, TIM_CCx_Enable);
	TIM_CCxNCmd(TIM1, TIM_Channel_1, TIM_CCxN_Enable);
	TIM_GenerateEvent(TIM1, TIM_EventSource_COM);

#ifdef HW_HAS_DUAL_MOTORS
	float current_sum_m2[3] = {0.0, 0.0, 0.0};
	float voltage_sum_m2[3] = {0.0, 0.0, 0.0};
	TIMER_UPDATE_DUTY_M2(TIM8->ARR / 2, TIM8->ARR / 2, TIM8->ARR / 2);

	stop_pwm_hw((motor_all_state_t*)&m_motor_2);
	PHASE_FILTER_ON_M2();
	TIM_SelectOCxM(TIM8, TIM_Channel_1, TIM_OCMode_PWM1);
	TIM_CCxCmd(TIM8, TIM_Channel_1, TIM_CCx_Enable);
	TIM_CCxNCmd(TIM8, TIM_Channel_1, TIM_CCxN_Enable);
	TIM_GenerateEvent(TIM8, TIM_EventSource_COM);
#endif

	chThdSleep(1);

	for (float i = 0;i < samples;i++) {
		current_sum[0] += m_motor_1.m_currents_adc[0];
		voltage_sum[0] += ADC_VOLTS(ADC_IND_SENS1);
#ifdef HW_HAS_DUAL_MOTORS
		current_sum_m2[0] += m_motor_2.m_currents_adc[0];
		voltage_sum_m2[0] += ADC_VOLTS(ADC_IND_SENS4);
#endif
		chThdSleep(1);
	}

	// Start PWM on phase 2
	stop_pwm_hw((motor_all_state_t*)&m_motor_1);
	PHASE_FILTER_ON();
	TIM_SelectOCxM(TIM1, TIM_Channel_2, TIM_OCMode_PWM1);
	TIM_CCxCmd(TIM1, TIM_Channel_2, TIM_CCx_Enable);
	TIM_CCxNCmd(TIM1, TIM_Channel_2, TIM_CCxN_Enable);
	TIM_GenerateEvent(TIM1, TIM_EventSource_COM);

#ifdef HW_HAS_DUAL_MOTORS
	stop_pwm_hw((motor_all_state_t*)&m_motor_2);
	PHASE_FILTER_ON_M2();
	TIM_SelectOCxM(TIM8, TIM_Channel_2, TIM_OCMode_PWM1);
	TIM_CCxCmd(TIM8, TIM_Channel_2, TIM_CCx_Enable);
	TIM_CCxNCmd(TIM8, TIM_Channel_2, TIM_CCxN_Enable);
	TIM_GenerateEvent(TIM8, TIM_EventSource_COM);
#endif

	chThdSleep(1);

	for (float i = 0;i < samples;i++) {
		current_sum[1] += m_motor_1.m_currents_adc[1];
		voltage_sum[1] += ADC_VOLTS(ADC_IND_SENS2);
#ifdef HW_HAS_DUAL_MOTORS
		current_sum_m2[1] += m_motor_2.m_currents_adc[1];
		voltage_sum_m2[1] += ADC_VOLTS(ADC_IND_SENS5);
#endif
		chThdSleep(1);
	}

	// Start PWM on phase 3
	stop_pwm_hw((motor_all_state_t*)&m_motor_1);
	PHASE_FILTER_ON();
	TIM_SelectOCxM(TIM1, TIM_Channel_3, TIM_OCMode_PWM1);
	TIM_CCxCmd(TIM1, TIM_Channel_3, TIM_CCx_Enable);
	TIM_CCxNCmd(TIM1, TIM_Channel_3, TIM_CCxN_Enable);
	TIM_GenerateEvent(TIM1, TIM_EventSource_COM);

#ifdef HW_HAS_DUAL_MOTORS
	stop_pwm_hw((motor_all_state_t*)&m_motor_2);
	PHASE_FILTER_ON_M2();
	TIM_SelectOCxM(TIM8, TIM_Channel_3, TIM_OCMode_PWM1);
	TIM_CCxCmd(TIM8, TIM_Channel_3, TIM_CCx_Enable);
	TIM_CCxNCmd(TIM8, TIM_Channel_3, TIM_CCxN_Enable);
	TIM_GenerateEvent(TIM8, TIM_EventSource_COM);
#endif

	chThdSleep(1);

	for (float i = 0;i < samples;i++) {
		current_sum[2] += m_motor_1.m_currents_adc[2];
		voltage_sum[2] += ADC_VOLTS(ADC_IND_SENS3);
#ifdef HW_HAS_DUAL_MOTORS
		current_sum_m2[2] += m_motor_2.m_currents_adc[2];
		voltage_sum_m2[2] += ADC_VOLTS(ADC_IND_SENS6);
#endif
		chThdSleep(1);
	}

	stop_pwm_hw((motor_all_state_t*)&m_motor_1);

	m_motor_1.m_conf->foc_offsets_current[0] = current_sum[0] / samples;
	m_motor_1.m_conf->foc_offsets_current[1] = current_sum[1] / samples;
	m_motor_1.m_conf->foc_offsets_current[2] = current_sum[2] / samples;

	voltage_sum[0] /= samples;
	voltage_sum[1] /= samples;
	voltage_sum[2] /= samples;
	float v_avg = (voltage_sum[0] + voltage_sum[1] + voltage_sum[2]) / 3.0;

	m_motor_1.m_conf->foc_offsets_voltage[0] = voltage_sum[0] - v_avg;
	m_motor_1.m_conf->foc_offsets_voltage[1] = voltage_sum[1] - v_avg;
	m_motor_1.m_conf->foc_offsets_voltage[2] = voltage_sum[2] - v_avg;

#ifdef HW_HAS_DUAL_MOTORS
	stop_pwm_hw((motor_all_state_t*)&m_motor_2);

	m_motor_2.m_conf->foc_offsets_current[0] = current_sum_m2[0] / samples;
	m_motor_2.m_conf->foc_offsets_current[1] = current_sum_m2[1] / samples;
	m_motor_2.m_conf->foc_offsets_current[2] = current_sum_m2[2] / samples;

	voltage_sum_m2[0] /= samples;
	voltage_sum_m2[1] /= samples;
	voltage_sum_m2[2] /= samples;
	v_avg = (voltage_sum_m2[0] + voltage_sum_m2[1] + voltage_sum_m2[2]) / 3.0;

	m_motor_2.m_conf->foc_offsets_voltage[0] = voltage_sum_m2[0] - v_avg;
	m_motor_2.m_conf->foc_offsets_voltage[1] = voltage_sum_m2[1] - v_avg;
	m_motor_2.m_conf->foc_offsets_voltage[2] = voltage_sum_m2[2] - v_avg;
#endif

	// Measure undriven offsets

	if (cal_undriven) {
		chThdSleepMilliseconds(10);

		voltage_sum[0] = 0.0; voltage_sum[1] = 0.0; voltage_sum[2] = 0.0;
#ifdef HW_HAS_DUAL_MOTORS
		voltage_sum_m2[0] = 0.0; voltage_sum_m2[1] = 0.0; voltage_sum_m2[2] = 0.0;
#endif

		for (float i = 0;i < samples;i++) {
			v_avg = (ADC_VOLTS(ADC_IND_SENS1) + ADC_VOLTS(ADC_IND_SENS2) + ADC_VOLTS(ADC_IND_SENS3)) / 3.0;
			voltage_sum[0] += ADC_VOLTS(ADC_IND_SENS1) - v_avg;
			voltage_sum[1] += ADC_VOLTS(ADC_IND_SENS2) - v_avg;
			voltage_sum[2] += ADC_VOLTS(ADC_IND_SENS3) - v_avg;
#ifdef HW_HAS_DUAL_MOTORS
			v_avg = (ADC_VOLTS(ADC_IND_SENS4) + ADC_VOLTS(ADC_IND_SENS5) + ADC_VOLTS(ADC_IND_SENS6)) / 3.0;
			voltage_sum_m2[0] += ADC_VOLTS(ADC_IND_SENS4) - v_avg;
			voltage_sum_m2[1] += ADC_VOLTS(ADC_IND_SENS5) - v_avg;
			voltage_sum_m2[2] += ADC_VOLTS(ADC_IND_SENS6) - v_avg;
#endif
			chThdSleep(1);
		}

		stop_pwm_hw((motor_all_state_t*)&m_motor_1);

		voltage_sum[0] /= samples;
		voltage_sum[1] /= samples;
		voltage_sum[2] /= samples;

		m_motor_1.m_conf->foc_offsets_voltage_undriven[0] = voltage_sum[0];
		m_motor_1.m_conf->foc_offsets_voltage_undriven[1] = voltage_sum[1];
		m_motor_1.m_conf->foc_offsets_voltage_undriven[2] = voltage_sum[2];
#ifdef HW_HAS_DUAL_MOTORS
		stop_pwm_hw((motor_all_state_t*)&m_motor_2);

		voltage_sum_m2[0] /= samples;
		voltage_sum_m2[1] /= samples;
		voltage_sum_m2[2] /= samples;

		m_motor_2.m_conf->foc_offsets_voltage_undriven[0] = voltage_sum_m2[0];
		m_motor_2.m_conf->foc_offsets_voltage_undriven[1] = voltage_sum_m2[1];
		m_motor_2.m_conf->foc_offsets_voltage_undriven[2] = voltage_sum_m2[2];
#endif
	}

	// TODO: Make sure that offsets are no more than e.g. 5%, as larger values indicate hardware problems.

	// Enable timeout
	timeout_configure(tout, tout_c, tout_ksw);
	mc_interface_unlock();

	m_dccal_done = true;

	return 1;
}

void mcpwm_foc_print_state(void) {
	commands_printf("Mod d:     %.2f", (double)get_motor_now()->m_motor_state.mod_d);
	commands_printf("Mod q:     %.2f", (double)get_motor_now()->m_motor_state.mod_q);
	commands_printf("Mod q flt: %.2f", (double)get_motor_now()->m_motor_state.mod_q_filter);
	commands_printf("Duty:      %.2f", (double)get_motor_now()->m_motor_state.duty_now);
	commands_printf("Vd:        %.2f", (double)get_motor_now()->m_motor_state.vd);
	commands_printf("Vq:        %.2f", (double)get_motor_now()->m_motor_state.vq);
	commands_printf("Phase:     %.2f", (double)get_motor_now()->m_motor_state.phase);
	commands_printf("V_alpha:   %.2f", (double)get_motor_now()->m_motor_state.v_alpha);
	commands_printf("V_beta:    %.2f", (double)get_motor_now()->m_motor_state.v_beta);
	commands_printf("id:        %.2f", (double)get_motor_now()->m_motor_state.id);
	commands_printf("iq:        %.2f", (double)get_motor_now()->m_motor_state.iq);
	commands_printf("id_filter: %.2f", (double)get_motor_now()->m_motor_state.id_filter);
	commands_printf("iq_filter: %.2f", (double)get_motor_now()->m_motor_state.iq_filter);
	commands_printf("id_target: %.2f", (double)get_motor_now()->m_motor_state.id_target);
	commands_printf("iq_target: %.2f", (double)get_motor_now()->m_motor_state.iq_target);
	commands_printf("i_abs:     %.2f", (double)get_motor_now()->m_motor_state.i_abs);
	commands_printf("i_abs_flt: %.2f", (double)get_motor_now()->m_motor_state.i_abs_filter);
	commands_printf("Obs_x1:    %.2f", (double)get_motor_now()->m_observer_state.x1);
	commands_printf("Obs_x2:    %.2f", (double)get_motor_now()->m_observer_state.x2);
	commands_printf("lambda_est:%.4f", (double)get_motor_now()->m_observer_state.lambda_est);
	commands_printf("vd_int:    %.2f", (double)get_motor_now()->m_motor_state.vd_int);
	commands_printf("vq_int:    %.2f", (double)get_motor_now()->m_motor_state.vq_int);
	commands_printf("off_delay: %.2f", (double)get_motor_now()->m_current_off_delay);
}

float mcpwm_foc_get_last_adc_isr_duration(void) {
	return m_last_adc_isr_duration;
}

void mcpwm_foc_tim_sample_int_handler(void) {
	if (m_init_done) {
		// Generate COM event here for synchronization
		TIM_GenerateEvent(TIM1, TIM_EventSource_COM);
		TIM_GenerateEvent(TIM8, TIM_EventSource_COM);

		virtual_motor_int_handler(
				m_motor_1.m_motor_state.v_alpha,
				m_motor_1.m_motor_state.v_beta);
	}
}

/**
 * FOC ADC中断处理函数 - FOC控制算法的核心
 *
 * 这是整个FOC控制系统的心脏，在每个PWM周期被调用，执行完整的FOC控制算法。
 *
 * 主要执行流程：
 * 1. 电流采样和校准
 * 2. Clarke变换 (abc → αβ)
 * 3. Park变换 (αβ → dq)
 * 4. PI控制器计算
 * 5. 反Park变换 (dq → αβ)
 * 6. SVPWM调制
 * 7. PWM占空比更新
 * 8. 观测器运行
 * 9. 保护检查
 *
 * 执行频率：通常为20-30kHz
 * 执行时间：<50μs (关键性能指标)
 *
 * @param p 中断参数指针(未使用)
 * @param flags 中断标志(未使用)
 */
void mcpwm_foc_adc_int_handler(void *p, uint32_t flags) {
	(void)p;		// 抑制未使用参数警告
	(void)flags;	// 抑制未使用参数警告

	/*
	 * 控制频率分频器
	 *
	 * 通过跳过某些中断来降低控制频率，减少CPU负载。
	 * FOC_CONTROL_LOOP_FREQ_DIVIDER定义了分频比例。
	 */
	static int skip = 0;
	if (++skip == FOC_CONTROL_LOOP_FREQ_DIVIDER) {
		skip = 0;
	} else {
		return;		// 跳过本次控制循环
	}

	/*
	 * 性能监测
	 *
	 * 记录中断开始时间，用于计算中断执行时间，
	 * 这是系统性能的关键指标。
	 */
	uint32_t t_start = timer_time_now();

	/*
	 * PWM状态检测
	 *
	 * 检测当前PWM计数器方向：
	 * - is_v7 = true: 向上计数(V7矢量)
	 * - is_v7 = false: 向下计数(V0矢量)
	 *
	 * 这用于确定当前的电机和采样时机。
	 */
	bool is_v7 = !(TIM1->CR1 & TIM_CR1_DIR);
	int norm_curr_ofs = 0;	// 归一化电流偏移量

	/*
	 * 双电机系统的电机选择
	 *
	 * 在双电机系统中，两个电机交替控制：
	 * - V7状态：控制电机2
	 * - V0状态：控制电机1
	 *
	 * 这种交替控制方式确保两个电机都能得到相同的控制频率。
	 */
#ifdef HW_HAS_DUAL_MOTORS
	bool is_second_motor = is_v7;						// 根据PWM状态确定当前电机
	norm_curr_ofs = is_second_motor ? 3 : 0;			// 电流归一化偏移(电机2使用通道3-5)
	motor_all_state_t *motor_now = is_second_motor ? (motor_all_state_t*)&m_motor_2 : (motor_all_state_t*)&m_motor_1;		// 当前控制的电机
	motor_all_state_t *motor_other = is_second_motor ? (motor_all_state_t*)&m_motor_1 : (motor_all_state_t*)&m_motor_2;	// 另一个电机
	m_isr_motor = is_second_motor ? 2 : 1;				// 设置中断处理的电机编号
#ifdef HW_HAS_3_SHUNTS
	volatile TIM_TypeDef *tim = is_second_motor ? TIM8 : TIM1;	// 选择对应的定时器(三分流器配置)
#endif
#else
	/*
	 * 单电机系统
	 *
	 * 在单电机系统中，所有指针都指向同一个电机。
	 */
	motor_all_state_t *motor_other = (motor_all_state_t*)&m_motor_1;	// 其他电机指针(指向同一电机)
	motor_all_state_t *motor_now = (motor_all_state_t*)&m_motor_1;		// 当前电机指针
	m_isr_motor = 1;												// 电机编号固定为1
#ifdef HW_HAS_3_SHUNTS
	volatile TIM_TypeDef *tim = TIM1;								// 使用TIM1定时器
#endif
#endif

	/*
	 * 配置参数指针
	 *
	 * 获取当前电机和其他电机的配置参数指针，
	 * 用于后续的控制算法计算。
	 */
	mc_configuration *conf_now = motor_now->m_conf;		// 当前电机配置
	mc_configuration *conf_other = motor_other->m_conf;	// 其他电机配置

	// Update modulation for V7 and collect current samples. This is used by the HFI.
	if (motor_other->m_duty_next_set) {
		motor_other->m_duty_next_set = false;
#ifdef HW_HAS_DUAL_MOTORS
		float curr0;
		float curr1;

		if (is_second_motor) {
			curr0 = (GET_CURRENT1() - conf_other->foc_offsets_current[0]) * FAC_CURRENT;
			curr1 = (GET_CURRENT2() - conf_other->foc_offsets_current[1]) * FAC_CURRENT;
			TIMER_UPDATE_DUTY_M1(motor_other->m_duty1_next, motor_other->m_duty2_next, motor_other->m_duty3_next);
		} else {
			curr0 = (GET_CURRENT1_M2() - conf_other->foc_offsets_current[0]) * FAC_CURRENT;
			curr1 = (GET_CURRENT2_M2() - conf_other->foc_offsets_current[1]) * FAC_CURRENT;
			TIMER_UPDATE_DUTY_M2(motor_other->m_duty1_next, motor_other->m_duty2_next, motor_other->m_duty3_next);
		}
#else
		float curr0 = (GET_CURRENT1() - conf_other->foc_offsets_current[0]) * FAC_CURRENT;
		float curr1 = (GET_CURRENT2() - conf_other->foc_offsets_current[1]) * FAC_CURRENT;

		TIMER_UPDATE_DUTY_M1(motor_other->m_duty1_next, motor_other->m_duty2_next, motor_other->m_duty3_next);
#ifdef HW_HAS_DUAL_PARALLEL
		TIMER_UPDATE_DUTY_M2(motor_other->m_duty1_next, motor_other->m_duty2_next, motor_other->m_duty3_next);
#endif
#endif

		motor_other->m_i_alpha_sample_next = curr0;
		motor_other->m_i_beta_sample_next = ONE_BY_SQRT3 * curr0 + TWO_BY_SQRT3 * curr1;
	}

#ifndef HW_HAS_DUAL_MOTORS
#ifdef HW_HAS_PHASE_SHUNTS
	if (!conf_now->foc_sample_v0_v7 && is_v7) {
		return;
	}
#else
	if (is_v7) {
		return;
	}
#endif
#endif

	/*
	 * ========================================================================
	 * 系统保护和外设控制
	 * ========================================================================
	 */

	// 喂狗操作 - 防止看门狗复位
	timeout_feed_WDT(THREAD_MCPWM);

#ifdef AD2S1205_SAMPLE_GPIO
	/*
	 * AD2S1205旋转变压器采样触发
	 *
	 * AD2S1205是一款高精度的旋转变压器到数字转换器，
	 * 通过下降沿触发强制进行位置采样。
	 */
	palClearPad(AD2S1205_SAMPLE_GPIO, AD2S1205_SAMPLE_PIN);
#endif

	/*
	 * ========================================================================
	 * 电流采样 - FOC控制的基础
	 * ========================================================================
	 *
	 * 电流采样是FOC控制的第一步，需要同步采样三相电流。
	 * 采样时机与PWM波形严格同步，确保采样的准确性。
	 *
	 * 采样原理：
	 * 1. 使用分流电阻测量相电流
	 * 2. 通过运算放大器放大电流信号
	 * 3. ADC同步采样转换为数字量
	 * 4. 软件校准和滤波处理
	 *
	 * 采样配置：
	 * - 双电机系统：每个电机独立采样
	 * - 三分流器：可测量所有三相电流
	 * - 双分流器：测量两相，第三相通过计算得出
	 * - 并联电机：多个电机电流相加
	 */

#ifdef HW_HAS_DUAL_MOTORS
	float curr0 = 0;	// A相电流原始ADC值
	float curr1 = 0;	// B相电流原始ADC值

	/*
	 * 双电机系统电流采样
	 *
	 * 根据当前控制的电机选择对应的ADC通道：
	 * - 电机1：使用ADC通道1和2
	 * - 电机2：使用ADC通道4和5
	 */
	if (is_second_motor) {
		curr0 = GET_CURRENT1_M2();		// 电机2的A相电流
		curr1 = GET_CURRENT2_M2();		// 电机2的B相电流
	} else {
		curr0 = GET_CURRENT1();			// 电机1的A相电流
		curr1 = GET_CURRENT2();			// 电机1的B相电流
	}
#else
	/*
	 * 单电机系统电流采样
	 *
	 * 使用固定的ADC通道采样电机电流。
	 */
	float curr0 = GET_CURRENT1();		// A相电流原始ADC值
	float curr1 = GET_CURRENT2();		// B相电流原始ADC值

#ifdef HW_HAS_DUAL_PARALLEL
	/*
	 * 并联电机配置
	 *
	 * 两个电机并联运行时，需要将两个电机的电流相加，
	 * 得到总的相电流。
	 */
	curr0 += GET_CURRENT1_M2();		// 加上第二个电机的A相电流
	curr1 += GET_CURRENT2_M2();		// 加上第二个电机的B相电流
#endif
#endif

	/*
	 * 三分流器电流采样
	 *
	 * 在三分流器配置中，可以直接测量所有三相电流，
	 * 提供更高的测量精度和更好的故障检测能力。
	 */
#ifdef HW_HAS_3_SHUNTS
#ifdef HW_HAS_DUAL_MOTORS
	float curr2 = is_second_motor ? GET_CURRENT3_M2() : GET_CURRENT3();	// C相电流
#else
	float curr2 = GET_CURRENT3();		// C相电流原始ADC值
#ifdef HW_HAS_DUAL_PARALLEL
	curr2 += GET_CURRENT3_M2();		// 并联电机的C相电流
#endif
#endif
#endif

	/*
	 * 保存原始ADC采样值
	 *
	 * 这些原始值用于诊断和调试，包含了偏移量。
	 */
	motor_now->m_currents_adc[0] = curr0;		// A相原始ADC值
	motor_now->m_currents_adc[1] = curr1;		// B相原始ADC值
#ifdef HW_HAS_3_SHUNTS
	motor_now->m_currents_adc[2] = curr2;		// C相原始ADC值
#else
	motor_now->m_currents_adc[2] = 0.0;		// 双分流器配置下C相为0
#endif

	/*
	 * ========================================================================
	 * 电流偏移校准 - 提高测量精度
	 * ========================================================================
	 *
	 * ADC采样存在固有偏移，需要通过校准来消除：
	 * 1. 偏移来源：运放偏移、ADC偏移、PCB布线等
	 * 2. 校准方法：电机静止时测量偏移值
	 * 3. 补偿公式：I_real = (ADC_value - offset) × scale_factor
	 *
	 * 偏移校准在系统初始化时进行，存储在配置参数中。
	 */
	curr0 -= conf_now->foc_offsets_current[0];	// A相偏移校准
	curr1 -= conf_now->foc_offsets_current[1];	// B相偏移校准
#ifdef HW_HAS_3_SHUNTS
	curr2 -= conf_now->foc_offsets_current[2];	// C相偏移校准

	/*
	 * 三相电流不平衡检测
	 *
	 * 理论上三相电流之和应为零：ia + ib + ic = 0
	 * 不平衡度可用于检测：
	 * 1. 电流传感器故障
	 * 2. 相线断路
	 * 3. 电机绕组不对称
	 */
	motor_now->m_curr_unbalance = curr0 + curr1 + curr2;
#endif

	/*
	 * 归一化电流值存储
	 *
	 * 将校准后的电流值存储到全局数组中，供后续算法使用。
	 * norm_curr_ofs用于双电机系统的通道偏移。
	 */
	ADC_curr_norm_value[0 + norm_curr_ofs] = curr0;	// A相归一化电流
	ADC_curr_norm_value[1 + norm_curr_ofs] = curr1;	// B相归一化电流
#ifdef HW_HAS_3_SHUNTS
	ADC_curr_norm_value[2 + norm_curr_ofs] = curr2;	// C相归一化电流
#else
	/*
	 * 双分流器配置下的第三相电流计算
	 *
	 * 利用基尔霍夫电流定律：ic = -(ia + ib)
	 * 这种方法在大部分工况下精度足够，但在某些特殊情况下
	 * （如低调制度）可能精度不足。
	 */
	ADC_curr_norm_value[2 + norm_curr_ofs] = -(ADC_curr_norm_value[0] + ADC_curr_norm_value[1]);
#endif

	/*
	 * ========================================================================
	 * 电流采样优化算法 - 提高测量精度和范围
	 * ========================================================================
	 *
	 * 根据调制状态选择最佳的电流采样方案，以提高测量精度。
	 * 这对于三分流器配置特别重要，可以避免在某些PWM状态下
	 * 采样时间不足的问题。
	 */
#ifdef HW_HAS_3_SHUNTS
	if (conf_now->foc_sample_high_current) {
		/*
		 * 高电流采样模式
		 *
		 * 原理：选择两个较小的电流来推导最大的那个电流
		 * 目的：扩展电流测量范围，避免ADC饱和
		 *
		 * 算法：
		 * 1. 计算三相电流的绝对值
		 * 2. 找出最大的电流
		 * 3. 用另外两相电流计算最大电流：i_max = -(i_other1 + i_other2)
		 *
		 * 优势：
		 * - 避免大电流时的ADC饱和
		 * - 提高动态范围
		 * - 减少测量噪声影响
		 */
		const float i0_abs = fabsf(ADC_curr_norm_value[0 + norm_curr_ofs]);	// A相电流绝对值
		const float i1_abs = fabsf(ADC_curr_norm_value[1 + norm_curr_ofs]);	// B相电流绝对值
		const float i2_abs = fabsf(ADC_curr_norm_value[2 + norm_curr_ofs]);	// C相电流绝对值

		if (i0_abs > i1_abs && i0_abs > i2_abs) {
			// A相电流最大，用B相和C相计算A相
			ADC_curr_norm_value[0 + norm_curr_ofs] = -(ADC_curr_norm_value[1 + norm_curr_ofs] + ADC_curr_norm_value[2 + norm_curr_ofs]);
		} else if (i1_abs > i0_abs && i1_abs > i2_abs) {
			// B相电流最大，用A相和C相计算B相
			ADC_curr_norm_value[1 + norm_curr_ofs] = -(ADC_curr_norm_value[0 + norm_curr_ofs] + ADC_curr_norm_value[2 + norm_curr_ofs]);
		} else if (i2_abs > i0_abs && i2_abs > i1_abs) {
			// C相电流最大，用A相和B相计算C相
			ADC_curr_norm_value[2 + norm_curr_ofs] = -(ADC_curr_norm_value[0 + norm_curr_ofs] + ADC_curr_norm_value[1 + norm_curr_ofs]);
		}
	} else {
#ifdef HW_HAS_PHASE_SHUNTS
		if (is_v7) {
			if (tim->CCR1 > 500 && tim->CCR2 > 500) {
				// Use the same 2 shunts on low modulation, as that will avoid jumps in the current reading.
				// This is especially important when using HFI.
				ADC_curr_norm_value[2 + norm_curr_ofs] = -(ADC_curr_norm_value[0 + norm_curr_ofs] + ADC_curr_norm_value[1 + norm_curr_ofs]);
			} else {
				if (tim->CCR1 < tim->CCR2 && tim->CCR1 < tim->CCR3) {
					ADC_curr_norm_value[0 + norm_curr_ofs] = -(ADC_curr_norm_value[1 + norm_curr_ofs] + ADC_curr_norm_value[2 + norm_curr_ofs]);
				} else if (tim->CCR2 < tim->CCR1 && tim->CCR2 < tim->CCR3) {
					ADC_curr_norm_value[1 + norm_curr_ofs] = -(ADC_curr_norm_value[0 + norm_curr_ofs] + ADC_curr_norm_value[2 + norm_curr_ofs]);
				} else if (tim->CCR3 < tim->CCR1 && tim->CCR3 < tim->CCR2) {
					ADC_curr_norm_value[2 + norm_curr_ofs] = -(ADC_curr_norm_value[0 + norm_curr_ofs] + ADC_curr_norm_value[1 + norm_curr_ofs]);
				}
			}
		} else {
			if (tim->CCR1 < (tim->ARR - 500) && tim->CCR2 < (tim->ARR - 500)) {
				// Use the same 2 shunts on low modulation, as that will avoid jumps in the current reading.
				// This is especially important when using HFI.
				ADC_curr_norm_value[2 + norm_curr_ofs] = -(ADC_curr_norm_value[0 + norm_curr_ofs] + ADC_curr_norm_value[1 + norm_curr_ofs]);
			} else {
				if (tim->CCR1 > tim->CCR2 && tim->CCR1 > tim->CCR3) {
					ADC_curr_norm_value[0 + norm_curr_ofs] = -(ADC_curr_norm_value[1 + norm_curr_ofs] + ADC_curr_norm_value[2 + norm_curr_ofs]);
				} else if (tim->CCR2 > tim->CCR1 && tim->CCR2 > tim->CCR3) {
					ADC_curr_norm_value[1 + norm_curr_ofs] = -(ADC_curr_norm_value[0 + norm_curr_ofs] + ADC_curr_norm_value[2 + norm_curr_ofs]);
				} else if (tim->CCR3 > tim->CCR1 && tim->CCR3 > tim->CCR2) {
					ADC_curr_norm_value[2 + norm_curr_ofs] = -(ADC_curr_norm_value[0 + norm_curr_ofs] + ADC_curr_norm_value[1 + norm_curr_ofs]);
				}
			}
		}
#else
		if (tim->CCR1 < (tim->ARR - 500) && tim->CCR2 < (tim->ARR - 500)) {
			// Use the same 2 shunts on low modulation, as that will avoid jumps in the current reading.
			// This is especially important when using HFI.
			ADC_curr_norm_value[2 + norm_curr_ofs] = -(ADC_curr_norm_value[0 + norm_curr_ofs] + ADC_curr_norm_value[1 + norm_curr_ofs]);
		} else {
			if (tim->CCR1 > tim->CCR2 && tim->CCR1 > tim->CCR3) {
				ADC_curr_norm_value[0 + norm_curr_ofs] = -(ADC_curr_norm_value[1 + norm_curr_ofs] + ADC_curr_norm_value[2 + norm_curr_ofs]);
			} else if (tim->CCR2 > tim->CCR1 && tim->CCR2 > tim->CCR3) {
				ADC_curr_norm_value[1 + norm_curr_ofs] = -(ADC_curr_norm_value[0 + norm_curr_ofs] + ADC_curr_norm_value[2 + norm_curr_ofs]);
			} else if (tim->CCR3 > tim->CCR1 && tim->CCR3 > tim->CCR2) {
				ADC_curr_norm_value[2 + norm_curr_ofs] = -(ADC_curr_norm_value[0 + norm_curr_ofs] + ADC_curr_norm_value[1 + norm_curr_ofs]);
			}
		}
#endif
	}
#endif

	float ia = ADC_curr_norm_value[0 + norm_curr_ofs] * FAC_CURRENT;
	float ib = ADC_curr_norm_value[1 + norm_curr_ofs] * FAC_CURRENT;
//	float ic = -(ia + ib);

#ifdef HW_HAS_PHASE_SHUNTS
	float dt;
	if (conf_now->foc_sample_v0_v7) {
		dt = 1.0 / conf_now->foc_f_zv;
	} else {
		dt = 1.0 / (conf_now->foc_f_zv / 2.0);
	}
#else
	float dt = 1.0 / (conf_now->foc_f_zv / 2.0);
#endif

	// This has to be done for the skip function to have any chance at working with the
	// observer and control loops.
	// TODO: Test this.
	dt *= (float)FOC_CONTROL_LOOP_FREQ_DIVIDER;

	UTILS_LP_FAST(motor_now->m_motor_state.v_bus, GET_INPUT_VOLTAGE(), 0.1);

	volatile float enc_ang = 0;
	volatile bool encoder_is_being_used = false;

	if (virtual_motor_is_connected()) {
		if (conf_now->foc_sensor_mode == FOC_SENSOR_MODE_ENCODER ) {
			enc_ang = virtual_motor_get_angle_deg();
			encoder_is_being_used = true;
		}
	} else {
		if (encoder_is_configured()) {
			enc_ang = encoder_read_deg();
			encoder_is_being_used = true;
		}
	}

	if (encoder_is_being_used) {
		float phase_tmp = enc_ang;
		if (conf_now->foc_encoder_inverted) {
			phase_tmp = 360.0 - phase_tmp;
		}
		phase_tmp *= conf_now->foc_encoder_ratio;
		phase_tmp -= conf_now->foc_encoder_offset;
		utils_norm_angle((float*)&phase_tmp);
		motor_now->m_phase_now_encoder = DEG2RAD_f(phase_tmp);
	}

	if (motor_now->m_state == MC_STATE_RUNNING) {
		/*
		 * ========================================================================
		 * Clarke变换 (abc → αβ坐标变换) - FOC算法第一步
		 * ========================================================================
		 *
		 * 功能：将三相静止坐标系(abc)转换为两相静止坐标系(αβ)
		 * 目的：将三相交流量简化为两相交流量，降低计算复杂度
		 *
		 * 数学原理：
		 * 三相对称系统满足：ia + ib + ic = 0，因此 ic = -(ia + ib)
		 * 利用这个约束条件，可以将三相量简化为两相量。
		 *
		 * 标准Clarke变换矩阵（功率不变变换）：
		 * [α]   (2/3) × [1    -1/2   -1/2 ] [a]
		 * [β] =        [0   √3/2   -√3/2] [b]
		 *                                  [c]
		 *
		 * 简化的Clarke变换（利用三相平衡条件 ic = -(ia + ib)）：
		 * α = ia
		 * β = (1/√3) × ia + (2/√3) × ib
		 *
		 * 标准形式的Clarke变换：
		 * α = (2/3) × (ia - 0.5×ib - 0.5×ic)
		 * β = (2/3) × (√3/2) × (ib - ic)
		 *
		 * 物理意义：
		 * - α轴：与A相重合的固定轴
		 * - β轴：超前α轴90°的固定轴
		 * - αβ坐标系是静止的，不随转子旋转
		 * - 保持功率不变：P3φ = P2φ
		 *
		 * 数学常数：
		 * ONE_BY_SQRT3 = 1/√3 ≈ 0.5774
		 * TWO_BY_SQRT3 = 2/√3 ≈ 1.1547
		 *
		 * 变换特点：
		 * 1. 将三相正弦波转换为两相正弦波
		 * 2. 减少了一个自由度，简化了控制
		 * 3. 为后续Park变换做准备
		 */
		motor_now->m_motor_state.i_alpha = ia;								// α轴电流分量
		motor_now->m_motor_state.i_beta = ONE_BY_SQRT3 * ia + TWO_BY_SQRT3 * ib;	// β轴电流分量

		motor_now->m_i_alpha_sample_with_offset = motor_now->m_motor_state.i_alpha;
		motor_now->m_i_beta_sample_with_offset = motor_now->m_motor_state.i_beta;

		if (motor_now->m_i_alpha_beta_has_offset) {
			motor_now->m_motor_state.i_alpha = 0.5 * (motor_now->m_motor_state.i_alpha + motor_now->m_i_alpha_sample_next);
			motor_now->m_motor_state.i_beta = 0.5 * (motor_now->m_motor_state.i_beta + motor_now->m_i_beta_sample_next);
			motor_now->m_i_alpha_beta_has_offset = false;
		}

		const float duty_now = motor_now->m_motor_state.duty_now;
		const float duty_abs = fabsf(duty_now);
		const float vq_now = motor_now->m_motor_state.vq;
		const float speed_fast_now = motor_now->m_pll_speed;

		float id_set_tmp = motor_now->m_id_set;
		float iq_set_tmp = motor_now->m_iq_set;
		motor_now->m_motor_state.max_duty = conf_now->l_max_duty;

		if (motor_now->m_control_mode == CONTROL_MODE_CURRENT_BRAKE) {
			utils_truncate_number_abs(&iq_set_tmp, -conf_now->lo_current_min);
		}

		UTILS_LP_FAST(motor_now->m_duty_abs_filtered, duty_abs, 0.01);
		utils_truncate_number_abs((float*)&motor_now->m_duty_abs_filtered, 1.0);

		UTILS_LP_FAST(motor_now->m_duty_filtered, duty_now, 0.01);
		utils_truncate_number_abs((float*)&motor_now->m_duty_filtered, 1.0);

		float duty_set = motor_now->m_duty_cycle_set;
		bool control_duty = motor_now->m_control_mode == CONTROL_MODE_DUTY ||
				motor_now->m_control_mode == CONTROL_MODE_OPENLOOP_DUTY ||
				motor_now->m_control_mode == CONTROL_MODE_OPENLOOP_DUTY_PHASE;

		// Short all phases (duty=0) the moment the direction or modulation changes sign. That will avoid
		// active braking or changing direction. Keep all phases shorted (duty == 0) until the
		// braking current reaches the set or maximum value, then go back to current control
		// mode. Stay in duty=0 for at least 10 cycles to avoid jumping in and out of that mode rapidly
		// around the threshold.
		if (motor_now->m_control_mode == CONTROL_MODE_CURRENT_BRAKE) {
			if ((SIGN(speed_fast_now) != SIGN(motor_now->m_br_speed_before) ||
					SIGN(vq_now) != SIGN(motor_now->m_br_vq_before) ||
					fabsf(motor_now->m_duty_filtered) < 0.001 || motor_now->m_br_no_duty_samples < 10) &&
					motor_now->m_motor_state.i_abs_filter < fabsf(iq_set_tmp)) {
				control_duty = true;
				duty_set = 0.0;
				motor_now->m_br_no_duty_samples = 0;
			} else if (motor_now->m_br_no_duty_samples < 10) {
				control_duty = true;
				duty_set = 0.0;
				motor_now->m_br_no_duty_samples++;
			}
		} else {
			motor_now->m_br_no_duty_samples = 0;
		}

		motor_now->m_br_speed_before = speed_fast_now;
		motor_now->m_br_vq_before = vq_now;

		// Brake when set ERPM is below min ERPM
		if (motor_now->m_control_mode == CONTROL_MODE_SPEED &&
				fabsf(motor_now->m_speed_pid_set_rpm) < conf_now->s_pid_min_erpm) {
			control_duty = true;
			duty_set = 0.0;
		}

		// Reset integrator when leaving duty cycle mode, as the windup protection is not too fast. Making
		// better windup protection is probably better, but not easy.
		if (!control_duty && motor_now->m_was_control_duty) {
			motor_now->m_motor_state.vq_int = motor_now->m_motor_state.vq;
			if (conf_now->foc_cc_decoupling == FOC_CC_DECOUPLING_BEMF ||
					conf_now->foc_cc_decoupling == FOC_CC_DECOUPLING_CROSS_BEMF) {
				motor_now->m_motor_state.vq_int -= motor_now->m_motor_state.speed_rad_s * conf_now->foc_motor_flux_linkage;
			}
		}
		motor_now->m_was_control_duty = control_duty;

		if (!control_duty) {
			motor_now->m_duty_i_term = motor_now->m_motor_state.iq / conf_now->lo_current_max;
			motor_now->duty_was_pi = false;
		}

		if (control_duty) {
			// Duty cycle control
			if (fabsf(duty_set) < (duty_abs - 0.01) &&
					(!motor_now->duty_was_pi || SIGN(motor_now->duty_pi_duty_last) == SIGN(duty_now))) {
				// Truncating the duty cycle here would be dangerous, so run a PI controller.

				motor_now->duty_pi_duty_last = duty_now;
				motor_now->duty_was_pi = true;

				// Compute error
				float error = duty_set - motor_now->m_motor_state.duty_now;

				// Compute parameters
				float scale = 1.0 / motor_now->m_motor_state.v_bus;
				float p_term = error * conf_now->foc_duty_dowmramp_kp * scale;
				motor_now->m_duty_i_term += error * (conf_now->foc_duty_dowmramp_ki * dt) * scale;

				// I-term wind-up protection
				utils_truncate_number((float*)&motor_now->m_duty_i_term, -1.0, 1.0);

				// Calculate output
				float output = p_term + motor_now->m_duty_i_term;
				utils_truncate_number(&output, -1.0, 1.0);
				iq_set_tmp = output * conf_now->lo_current_max;
			} else {
				// If the duty cycle is less than or equal to the set duty cycle just limit
				// the modulation and use the maximum allowed current.
				motor_now->m_duty_i_term = motor_now->m_motor_state.iq / conf_now->lo_current_max;
				motor_now->m_motor_state.max_duty = duty_set;
				if (duty_set > 0.0) {
					iq_set_tmp = conf_now->lo_current_max;
				} else {
					iq_set_tmp = -conf_now->lo_current_max;
				}
				motor_now->duty_was_pi = false;
			}
		} else if (motor_now->m_control_mode == CONTROL_MODE_CURRENT_BRAKE) {
			// Braking
			iq_set_tmp = -SIGN(speed_fast_now) * fabsf(iq_set_tmp);
		}

		// Set motor phase
		{
			if (!motor_now->m_phase_override) {
				/*
				 * ========================================================================
				 * 无传感器观测器算法 - 转子位置和速度估计
				 * ========================================================================
				 *
				 * 功能：基于电机数学模型估计转子位置和速度
				 * 目的：实现无位置传感器的高性能FOC控制
				 *
				 * 数学原理：
				 *
				 * 1. 永磁同步电机数学模型（αβ坐标系）：
				 *    v_α = R×i_α + L×di_α/dt + e_α
				 *    v_β = R×i_β + L×di_β/dt + e_β
				 *
				 *    其中反电动势：
				 *    e_α = -ω_e×λ_pm×sin(θ_e)
				 *    e_β =  ω_e×λ_pm×cos(θ_e)
				 *
				 * 2. 观测器状态方程：
				 *    定义状态变量 x = [x₁, x₂]^T，其中：
				 *    x₁ = L×i_α + λ_pm×cos(θ_e)  (α轴磁链)
				 *    x₂ = L×i_β + λ_pm×sin(θ_e)  (β轴磁链)
				 *
				 * 3. 观测器更新方程：
				 *    x̂₁(k+1) = x̂₁(k) + Ts×(v_α - R×i_α)/L + G×(i_α - x̂₁(k)/L)
				 *    x̂₂(k+1) = x̂₂(k) + Ts×(v_β - R×i_β)/L + G×(i_β - x̂₂(k)/L)
				 *
				 *    其中：
				 *    - Ts: 采样周期
				 *    - G: 观测器增益（影响收敛速度和稳定性）
				 *    - R, L: 电机电阻和电感
				 *
				 * 4. 转子位置估计：
				 *    ê_α = x̂₁ - L×i_α  (估计的α轴反电动势)
				 *    ê_β = x̂₂ - L×i_β  (估计的β轴反电动势)
				 *    θ_e = atan2(ê_β, ê_α) + π/2  (电角度)
				 *
				 * 5. 转子速度估计：
				 *    ω_e = (ê_α×i_β - ê_β×i_α) / λ_pm  (电角速度)
				 *
				 * 观测器特点：
				 * - 适用于中高速运行（>300 ERPM）
				 * - 依赖反电动势信号，低速时精度下降
				 * - 对电机参数敏感，需要准确的R、L、λ参数
				 * - 收敛速度快，动态响应好
				 *
				 * 稳定性条件：
				 * - 观测器增益G需要合理选择
				 * - 过小：收敛慢，抗干扰能力差
				 * - 过大：可能不稳定，噪声敏感
				 *
				 * 参考文献：
				 * "Sensorless Control of AC Motor Drives" - Kazmierkowski et al.
				 */
				foc_observer_update(motor_now->m_motor_state.v_alpha, motor_now->m_motor_state.v_beta,
						motor_now->m_motor_state.i_alpha, motor_now->m_motor_state.i_beta,
						dt, &(motor_now->m_observer_state), &motor_now->m_phase_now_observer, motor_now);

				/*
				 * 开关频率相位滞后补偿
				 *
				 * 补偿由开关频率引起的相位滞后。这对于相对于开关频率
				 * 运行在高ERPM的电机非常重要。
				 *
				 * 补偿量 = ω × dt × (0.5 + observer_offset)
				 * 其中：
				 * - 0.5: 基本的半个开关周期补偿
				 * - observer_offset: 额外的用户可调偏移
				 */
				motor_now->m_phase_now_observer += motor_now->m_pll_speed * dt * (0.5 + conf_now->foc_observer_offset);
				utils_norm_angle_rad((float*)&motor_now->m_phase_now_observer);	// 角度归一化到[-π, π]
			}

			switch (conf_now->foc_sensor_mode) {
			case FOC_SENSOR_MODE_ENCODER:
				if (encoder_index_found() || virtual_motor_is_connected()) {
					motor_now->m_motor_state.phase = foc_correct_encoder(
							motor_now->m_phase_now_observer,
							motor_now->m_phase_now_encoder,
							motor_now->m_speed_est_fast,
							conf_now->foc_sl_erpm,
							motor_now);
				} else {
					// Rotate the motor in open loop if the index isn't found.
					motor_now->m_motor_state.phase = motor_now->m_phase_now_encoder_no_index;
				}

				if (!motor_now->m_phase_override && motor_now->m_control_mode != CONTROL_MODE_OPENLOOP_PHASE) {
					id_set_tmp = 0.0;
				}
				break;
			case FOC_SENSOR_MODE_HALL:
				motor_now->m_phase_now_observer = foc_correct_hall(motor_now->m_phase_now_observer, dt, motor_now,
						utils_read_hall(motor_now != &m_motor_1, conf_now->m_hall_extra_samples));
				motor_now->m_motor_state.phase = motor_now->m_phase_now_observer;

				if (!motor_now->m_phase_override && motor_now->m_control_mode != CONTROL_MODE_OPENLOOP_PHASE) {
					id_set_tmp = 0.0;
				}
				break;
			case FOC_SENSOR_MODE_SENSORLESS:
				if (motor_now->m_phase_observer_override) {
					motor_now->m_motor_state.phase = motor_now->m_phase_now_observer_override;
					motor_now->m_observer_state.x1 = motor_now->m_observer_x1_override;
					motor_now->m_observer_state.x2 = motor_now->m_observer_x2_override;
					iq_set_tmp += conf_now->foc_sl_openloop_boost_q * SIGN(iq_set_tmp);
					if (conf_now->foc_sl_openloop_max_q > conf_now->cc_min_current) {
						utils_truncate_number_abs(&iq_set_tmp, conf_now->foc_sl_openloop_max_q);
					}
				} else {
					motor_now->m_motor_state.phase = motor_now->m_phase_now_observer;
				}

				if (!motor_now->m_phase_override && motor_now->m_control_mode != CONTROL_MODE_OPENLOOP_PHASE) {
					id_set_tmp = 0.0;
				}
				break;

			case FOC_SENSOR_MODE_HFI_START:
				motor_now->m_motor_state.phase = motor_now->m_phase_now_observer;

				if (motor_now->m_phase_observer_override) {
					motor_now->m_hfi.est_done_cnt = 0;
					motor_now->m_hfi.flip_cnt = 0;

					motor_now->m_min_rpm_hyst_timer = 0.0;
					motor_now->m_min_rpm_timer = 0.0;
					motor_now->m_phase_observer_override = false;
				}

				if (!motor_now->m_phase_override && motor_now->m_control_mode != CONTROL_MODE_OPENLOOP_PHASE) {
					id_set_tmp = 0.0;
				}
				break;

			case FOC_SENSOR_MODE_HFI:
			case FOC_SENSOR_MODE_HFI_V2:
			case FOC_SENSOR_MODE_HFI_V3:
			case FOC_SENSOR_MODE_HFI_V4:
			case FOC_SENSOR_MODE_HFI_V5:
				if (fabsf(RADPS2RPM_f(motor_now->m_speed_est_fast)) > conf_now->foc_sl_erpm_hfi) {
					motor_now->m_hfi.observer_zero_time = 0;
				} else {
					motor_now->m_hfi.observer_zero_time += dt;
				}

				if (motor_now->m_hfi.observer_zero_time < conf_now->foc_hfi_obs_ovr_sec) {
					motor_now->m_hfi.angle = motor_now->m_phase_now_observer;
					motor_now->m_hfi.double_integrator = -motor_now->m_speed_est_fast;
				}

				motor_now->m_motor_state.phase = foc_correct_encoder(
						motor_now->m_phase_now_observer,
						motor_now->m_hfi.angle,
						motor_now->m_speed_est_fast,
						conf_now->foc_sl_erpm_hfi,
						motor_now);

				if (!motor_now->m_phase_override && motor_now->m_control_mode != CONTROL_MODE_OPENLOOP_PHASE) {
					id_set_tmp = 0.0;
				}
				break;
			}

			if (motor_now->m_control_mode == CONTROL_MODE_HANDBRAKE) {
				// Force the phase to 0 in handbrake mode so that the current simply locks the rotor.
				motor_now->m_motor_state.phase = 0.0;
			} else if (motor_now->m_control_mode == CONTROL_MODE_OPENLOOP ||
					motor_now->m_control_mode == CONTROL_MODE_OPENLOOP_DUTY) {
				motor_now->m_openloop_angle += dt * motor_now->m_openloop_speed;
				utils_norm_angle_rad((float*)&motor_now->m_openloop_angle);
				motor_now->m_motor_state.phase = motor_now->m_openloop_angle;
			} else if (motor_now->m_control_mode == CONTROL_MODE_OPENLOOP_PHASE ||
					motor_now->m_control_mode == CONTROL_MODE_OPENLOOP_DUTY_PHASE) {
				motor_now->m_motor_state.phase = motor_now->m_openloop_phase;
			}

			if (motor_now->m_phase_override) {
				motor_now->m_motor_state.phase = motor_now->m_phase_now_override;
			}

			utils_fast_sincos_better(motor_now->m_motor_state.phase,
					(float*)&motor_now->m_motor_state.phase_sin,
					(float*)&motor_now->m_motor_state.phase_cos);
		}

		/*
		 * ========================================================================
		 * MTPA (Maximum Torque Per Ampere) 最大转矩电流比控制
		 * ========================================================================
		 *
		 * 功能：在给定电流幅值下产生最大转矩，提高电机效率
		 * 目的：优化d轴和q轴电流分配，减少铜损和铁损
		 *
		 * 数学原理：
		 *
		 * 1. PMSM转矩方程：
		 *    T = (3/2) × p × [λpm×iq + (Ld-Lq)×id×iq]
		 *    其中：
		 *    - λpm: 永磁体磁链
		 *    - Ld, Lq: d轴和q轴电感
		 *    - id, iq: d轴和q轴电流
		 *
		 * 2. 约束条件：
		 *    id² + iq² = I_max²  (电流幅值限制)
		 *
		 * 3. MTPA最优解：
		 *    使用拉格朗日乘数法求解约束优化问题：
		 *    ∂T/∂id = ∂T/∂iq × (∂iq/∂id)
		 *
		 * 4. 最优d轴电流：
		 *    id_opt = [λpm - √(λpm² + 8×(Ld-Lq)²×iq²)] / [4×(Ld-Lq)]
		 *
		 * 5. 最优q轴电流：
		 *    iq_opt = √(I_max² - id_opt²)
		 *
		 * 适用条件：
		 * - 凸极电机 (Ld ≠ Lq)
		 * - 中低速运行 (电压未饱和)
		 * - 转矩优先模式
		 *
		 * 参考：https://github.com/vedderb/bldc/pull/179
		 */
		const float ld_lq_diff = conf_now->foc_motor_ld_lq_diff;	// Ld - Lq (H)
		if (conf_now->foc_mtpa_mode != MTPA_MODE_OFF && ld_lq_diff != 0.0) {
			const float lambda = conf_now->foc_motor_flux_linkage;	// 永磁体磁链 (Wb)

			/*
			 * 参考电流选择
			 *
			 * MTPA_MODE_IQ_TARGET: 使用设定的q轴电流
			 * MTPA_MODE_IQ_MEASURED: 使用实际测量的q轴电流(更保守)
			 */
			float iq_ref = iq_set_tmp;
			if (conf_now->foc_mtpa_mode == MTPA_MODE_IQ_MEASURED) {
				iq_ref = utils_min_abs(iq_set_tmp, motor_now->m_motor_state.iq_filter);
			}

			/*
			 * MTPA最优d轴电流计算
			 *
			 * 公式：id_opt = [λpm - √(λpm² + 8×(Ld-Lq)²×iq²)] / [4×(Ld-Lq)]
			 *
			 * 物理意义：
			 * - id < 0: 弱磁电流，减少磁通
			 * - id = 0: 表面贴装电机的最优点
			 * - id > 0: 增磁电流，增强磁通(很少使用)
			 */
			id_set_tmp = (lambda - sqrtf(SQ(lambda) + 8.0 * SQ(ld_lq_diff * iq_ref))) / (4.0 * ld_lq_diff);

			/*
			 * 重新计算q轴电流
			 *
			 * 保持原始转矩方向，但调整幅值以满足电流约束：
			 * iq_new = sign(iq_old) × √(iq_old² - id_opt²)
			 */
			iq_set_tmp = SIGN(iq_set_tmp) * sqrtf(SQ(iq_set_tmp) - SQ(id_set_tmp));
		}

		/*
		 * ========================================================================
		 * 弱磁控制 (Field Weakening Control)
		 * ========================================================================
		 *
		 * 功能：在高速运行时通过注入负d轴电流来扩展速度范围
		 * 目的：突破电压限制，实现恒功率运行
		 *
		 * 弱磁原理：
		 *
		 * 1. 电压限制：
		 *    在高速时，反电动势增大：E = ω × λpm
		 *    当 E + I×R + L×dI/dt ≥ V_max 时，电压饱和
		 *
		 * 2. 弱磁策略：
		 *    注入负d轴电流：id < 0
		 *    有效磁链：λeff = λpm + Ld×id
		 *    当 id < 0 时，λeff < λpm，实现弱磁
		 *
		 * 3. 电压椭圆约束：
		 *    vd² + vq² ≤ V_max²
		 *    在dq平面形成电压椭圆限制
		 *
		 * 4. 弱磁控制器：
		 *    通常使用PI控制器，以电压幅值误差为输入：
		 *    i_fw = PI(V_max - √(vd² + vq²))
		 *
		 * 5. q轴电流补偿：
		 *    弱磁会影响q轴电流的有效性，需要补偿：
		 *    iq_comp = iq - sign(mod_q) × i_fw × factor
		 *
		 * 性能特点：
		 * - 扩展速度范围：通常可提高30-50%
		 * - 降低效率：弱磁电流产生额外损耗
		 * - 减少转矩：高速时转矩下降
		 * - 恒功率运行：P = T × ω = 常数
		 */
		const float mod_q = motor_now->m_motor_state.mod_q_filter;	// 滤波后的q轴调制度

		/*
		 * 弱磁电流应用
		 *
		 * m_i_fw_set: 弱磁控制器输出的弱磁电流 (A)
		 * 通常由1kHz的弱磁控制器计算得出，频率足够高
		 */
		// 注释：弱磁控制器运行频率1kHz已足够
		// run_fw(motor_now, dt);

		id_set_tmp -= motor_now->m_i_fw_set;	// 应用弱磁电流到d轴

		/*
		 * q轴电流补偿
		 *
		 * 弱磁会影响q轴电流的转矩效果，需要进行补偿：
		 * - foc_fw_q_current_factor: q轴补偿系数 (通常0.1-0.3)
		 * - SIGN(mod_q): 根据转矩方向确定补偿方向
		 */
		iq_set_tmp -= SIGN(mod_q) * motor_now->m_i_fw_set * conf_now->foc_fw_q_current_factor;

		/*
		 * ========================================================================
		 * 电流限制和保护 (Current Limiting and Protection)
		 * ========================================================================
		 *
		 * 功能：确保电机电流在安全范围内，保护电机和控制器
		 * 包含：输入电流限制、输出电流限制、电流矢量限制
		 */

		/*
		 * 输入电流限制 (Input Current Limiting)
		 *
		 * 根据调制度估算输入电流并进行限制：
		 * I_input ≈ I_motor × mod_q (忽略d轴电流的贡献)
		 *
		 * 限制策略：
		 * I_motor = I_input_limit / |mod_q|
		 *
		 * 注意：这里暂未考虑d轴电流对输入电流的影响
		 * TODO: 考虑d轴电流对输入电流的贡献
		 */
		if (mod_q > 0.001) {
			// 正向运行时的输入电流限制
			utils_truncate_number(&iq_set_tmp,
				conf_now->lo_in_current_min / mod_q,	// 最小输入电流对应的q轴电流
				conf_now->lo_in_current_max / mod_q);	// 最大输入电流对应的q轴电流
		} else if (mod_q < -0.001) {
			// 反向运行时的输入电流限制 (注意符号反转)
			utils_truncate_number(&iq_set_tmp,
				conf_now->lo_in_current_max / mod_q,	// 反向时的限制
				conf_now->lo_in_current_min / mod_q);
		}

		/*
		 * 输出电流限制 (Output Current Limiting)
		 *
		 * 直接限制电机的输出电流，防止过载：
		 * - lo_current_max: 最大输出电流 (A)
		 * - lo_current_min: 最小输出电流 (A，通常为负值)
		 */
		if (mod_q > 0.0) {
			// 正向转矩时的电流限制
			utils_truncate_number(&iq_set_tmp, conf_now->lo_current_min, conf_now->lo_current_max);
		} else {
			// 反向转矩时的电流限制 (符号反转)
			utils_truncate_number(&iq_set_tmp, -conf_now->lo_current_max, -conf_now->lo_current_min);
		}

		/*
		 * 电流矢量限制 (Current Vector Limiting)
		 *
		 * 确保电流矢量幅值不超过硬件限制：
		 * √(id² + iq²) ≤ I_max
		 *
		 * 限制策略：
		 * 1. 首先限制id在[-I_max, +I_max]范围内
		 * 2. 然后限制iq在剩余的电流空间内：iq_max = √(I_max² - id²)
		 *
		 * 这种方法优先保证d轴电流(弱磁电流)，因为它对高速性能至关重要。
		 */
		float current_max_abs = fabsf(utils_max_abs(conf_now->lo_current_max, conf_now->lo_current_min));
		utils_truncate_number_abs(&id_set_tmp, current_max_abs);						// 限制d轴电流幅值
		utils_truncate_number_abs(&iq_set_tmp, sqrtf(SQ(current_max_abs) - SQ(id_set_tmp)));	// 限制q轴电流幅值

		motor_now->m_motor_state.id_target = id_set_tmp;
		motor_now->m_motor_state.iq_target = iq_set_tmp;

		control_current(motor_now, dt);
	} else {
		// Motor is not running

		// The current is 0 when the motor is undriven
		motor_now->m_motor_state.i_alpha = 0.0;
		motor_now->m_motor_state.i_beta = 0.0;
		motor_now->m_motor_state.id = 0.0;
		motor_now->m_motor_state.iq = 0.0;
		motor_now->m_motor_state.id_filter = 0.0;
		motor_now->m_motor_state.iq_filter = 0.0;
		motor_now->m_duty_i_term = 0.0;
#ifdef HW_HAS_INPUT_CURRENT_SENSOR
		GET_INPUT_CURRENT_OFFSET(); // TODO: should this be done here?
#endif
		motor_now->m_motor_state.i_bus = 0.0;
		motor_now->m_motor_state.i_abs = 0.0;
		motor_now->m_motor_state.i_abs_filter = 0.0;

		// Track back emf
		update_valpha_vbeta(motor_now, 0.0, 0.0);

		// Run observer
		foc_observer_update(motor_now->m_motor_state.v_alpha, motor_now->m_motor_state.v_beta,
						motor_now->m_motor_state.i_alpha, motor_now->m_motor_state.i_beta,
						dt, &(motor_now->m_observer_state), 0, motor_now);
		motor_now->m_phase_now_observer = utils_fast_atan2(motor_now->m_x2_prev + motor_now->m_observer_state.x2,
														   motor_now->m_x1_prev + motor_now->m_observer_state.x1);

		// The observer phase offset has to be added here as well, with 0.5 switching cycles offset
		// compared to when running. Otherwise going from undriven to driven causes a current
		// spike.
		motor_now->m_phase_now_observer += motor_now->m_pll_speed * dt * conf_now->foc_observer_offset;
		utils_norm_angle_rad((float*)&motor_now->m_phase_now_observer);

		motor_now->m_x1_prev = motor_now->m_observer_state.x1;
		motor_now->m_x2_prev = motor_now->m_observer_state.x2;

		// Set motor phase
		{
			switch (conf_now->foc_sensor_mode) {
			case FOC_SENSOR_MODE_ENCODER:
				motor_now->m_motor_state.phase = foc_correct_encoder(
						motor_now->m_phase_now_observer,
						motor_now->m_phase_now_encoder,
						motor_now->m_speed_est_fast,
						conf_now->foc_sl_erpm,
						motor_now);
				break;
			case FOC_SENSOR_MODE_HALL:
				motor_now->m_phase_now_observer = foc_correct_hall(motor_now->m_phase_now_observer, dt, motor_now,
						utils_read_hall(motor_now != &m_motor_1, conf_now->m_hall_extra_samples));
				motor_now->m_motor_state.phase = motor_now->m_phase_now_observer;
				break;
			case FOC_SENSOR_MODE_SENSORLESS:
				motor_now->m_motor_state.phase = motor_now->m_phase_now_observer;
				break;
			case FOC_SENSOR_MODE_HFI:
			case FOC_SENSOR_MODE_HFI_V2:
			case FOC_SENSOR_MODE_HFI_V3:
			case FOC_SENSOR_MODE_HFI_V4:
			case FOC_SENSOR_MODE_HFI_V5:
			case FOC_SENSOR_MODE_HFI_START:{
				motor_now->m_motor_state.phase = motor_now->m_phase_now_observer;
				if (fabsf(RADPS2RPM_f(motor_now->m_pll_speed)) < (conf_now->foc_sl_erpm_hfi * 1.1)) {
					motor_now->m_hfi.est_done_cnt = 0;
					motor_now->m_hfi.flip_cnt = 0;
				}
			} break;

			}

			utils_fast_sincos_better(motor_now->m_motor_state.phase,
					(float*)&motor_now->m_motor_state.phase_sin,
					(float*)&motor_now->m_motor_state.phase_cos);
		}

		// HFI Restore
#ifdef HW_HAS_DUAL_MOTORS
		if (is_second_motor) {
			CURRENT_FILTER_ON_M2();
		} else {
			CURRENT_FILTER_ON();
		}
#else
		CURRENT_FILTER_ON();
#endif

		motor_now->m_hfi.ind = 0;
		motor_now->m_hfi.ready = false;
		motor_now->m_hfi.double_integrator = 0.0;
		motor_now->m_hfi.is_samp_n = false;
		motor_now->m_hfi.prev_sample = 0.0;
		motor_now->m_hfi.angle = motor_now->m_motor_state.phase;

		float s = motor_now->m_motor_state.phase_sin;
		float c = motor_now->m_motor_state.phase_cos;

		// Park transform
		float vd_tmp = c * motor_now->m_motor_state.v_alpha + s * motor_now->m_motor_state.v_beta;
		float vq_tmp = c * motor_now->m_motor_state.v_beta  - s * motor_now->m_motor_state.v_alpha;

		UTILS_NAN_ZERO(motor_now->m_motor_state.vd);
		UTILS_NAN_ZERO(motor_now->m_motor_state.vq);

		UTILS_LP_FAST(motor_now->m_motor_state.vd, vd_tmp, 0.2);
		UTILS_LP_FAST(motor_now->m_motor_state.vq, vq_tmp, 0.2);

		// Set the current controller integrator to the BEMF voltage to avoid
		// a current spike when the motor is driven again. Notice that we have
		// to take decoupling into account.
		motor_now->m_motor_state.vd_int = motor_now->m_motor_state.vd;
		motor_now->m_motor_state.vq_int = motor_now->m_motor_state.vq;

		if (conf_now->foc_cc_decoupling == FOC_CC_DECOUPLING_BEMF ||
				conf_now->foc_cc_decoupling == FOC_CC_DECOUPLING_CROSS_BEMF) {
			motor_now->m_motor_state.vq_int -= motor_now->m_motor_state.speed_rad_s * conf_now->foc_motor_flux_linkage;
		}

		// Update corresponding modulation
		/* voltage_normalize = 1/(2/3*V_bus) */
		const float voltage_normalize = 1.5 / motor_now->m_motor_state.v_bus;

		motor_now->m_motor_state.mod_d = motor_now->m_motor_state.vd * voltage_normalize;
		motor_now->m_motor_state.mod_q = motor_now->m_motor_state.vq * voltage_normalize;
		UTILS_NAN_ZERO(motor_now->m_motor_state.mod_q_filter);
		UTILS_LP_FAST(motor_now->m_motor_state.mod_q_filter, motor_now->m_motor_state.mod_q, 0.2);
		utils_truncate_number_abs((float*)&motor_now->m_motor_state.mod_q_filter, 1.0);
	}

	// Calculate duty cycle
	motor_now->m_motor_state.duty_now = SIGN(motor_now->m_motor_state.vq) *
			NORM2_f(motor_now->m_motor_state.mod_d, motor_now->m_motor_state.mod_q) * TWO_BY_SQRT3;

	float phase_for_speed_est = 0.0;
	switch (conf_now->foc_speed_soure) {
	case SPEED_SRC_CORRECTED:
		phase_for_speed_est = motor_now->m_motor_state.phase;
		break;
	case SPEED_SRC_OBSERVER:
		phase_for_speed_est = motor_now->m_phase_now_observer;
		break;
	};

	// Run PLL for speed estimation
	foc_pll_run(phase_for_speed_est, dt, &motor_now->m_pll_phase, &motor_now->m_pll_speed, conf_now);
	motor_now->m_motor_state.speed_rad_s = motor_now->m_pll_speed;

	// Low latency speed estimation, for e.g. HFI and speed control.
	{
		float diff = utils_angle_difference_rad(phase_for_speed_est, motor_now->m_phase_before_speed_est);
		utils_truncate_number(&diff, -M_PI / 3.0, M_PI / 3.0);

		UTILS_LP_FAST(motor_now->m_speed_est_fast, diff / dt, 0.01);
		UTILS_NAN_ZERO(motor_now->m_speed_est_fast);

		UTILS_LP_FAST(motor_now->m_speed_est_faster, diff / dt, 0.2);
		UTILS_NAN_ZERO(motor_now->m_speed_est_faster);

		// pll wind-up protection
		utils_truncate_number_abs((float*)&motor_now->m_pll_speed, fabsf(motor_now->m_speed_est_fast) * 3.0);

		motor_now->m_phase_before_speed_est = phase_for_speed_est;
	}

	// Update tachometer (resolution = 60 deg as for BLDC)
	float ph_tmp = motor_now->m_motor_state.phase;
	utils_norm_angle_rad(&ph_tmp);
	int step = (int)floorf((ph_tmp + M_PI) / (2.0 * M_PI) * 6.0);
	utils_truncate_number_int(&step, 0, 5);
	int diff = step - motor_now->m_tacho_step_last;
	motor_now->m_tacho_step_last = step;

	if (diff > 3) {
		diff -= 6;
	} else if (diff < -2) {
		diff += 6;
	}

	motor_now->m_tachometer += diff;
	motor_now->m_tachometer_abs += abs(diff);

	// Track position control angle
	float angle_now = 0.0;
	if (encoder_is_configured()) {
		if (conf_now->m_sensor_port_mode == SENSOR_PORT_MODE_TS5700N8501_MULTITURN) {
			angle_now = encoder_read_deg_multiturn();
		} else {
			angle_now = enc_ang;
		}
	} else {
		angle_now = RAD2DEG_f(motor_now->m_motor_state.phase);
	}

	utils_norm_angle(&angle_now);

	if (conf_now->p_pid_ang_div > 0.98 && conf_now->p_pid_ang_div < 1.02) {
		motor_now->m_pos_pid_now = angle_now;
	} else {
		if (angle_now < 90.0 && motor_now->m_pid_div_angle_last > 270.0) {
			motor_now->m_pid_div_angle_accumulator += 360.0 / conf_now->p_pid_ang_div;
			utils_norm_angle((float*)&motor_now->m_pid_div_angle_accumulator);
		} else if (angle_now > 270.0 && motor_now->m_pid_div_angle_last < 90.0) {
			motor_now->m_pid_div_angle_accumulator -= 360.0 / conf_now->p_pid_ang_div;
			utils_norm_angle((float*)&motor_now->m_pid_div_angle_accumulator);
		}

		motor_now->m_pid_div_angle_last = angle_now;

		motor_now->m_pos_pid_now = motor_now->m_pid_div_angle_accumulator + angle_now / conf_now->p_pid_ang_div;
		utils_norm_angle((float*)&motor_now->m_pos_pid_now);
	}

#ifdef AD2S1205_SAMPLE_GPIO
	// Release sample in the AD2S1205 resolver IC.
	palSetPad(AD2S1205_SAMPLE_GPIO, AD2S1205_SAMPLE_PIN);
#endif

#ifdef HW_HAS_DUAL_MOTORS
	mc_interface_mc_timer_isr(is_second_motor);
#else
	mc_interface_mc_timer_isr(false);
#endif

	m_isr_motor = 0;
	m_last_adc_isr_duration = timer_seconds_elapsed_since(t_start);
}

// Private functions

static void timer_update(motor_all_state_t *motor, float dt) {
	foc_run_fw(motor, dt);

	const mc_configuration *conf_now = motor->m_conf;

	// Calculate temperature-compensated parameters here
	if (mc_interface_temp_motor_filtered() > -30.0) {
		float comp_fact = 1.0 + 0.00386 * (mc_interface_temp_motor_filtered() - conf_now->foc_temp_comp_base_temp);
		motor->m_res_temp_comp = conf_now->foc_motor_r * comp_fact;
		motor->m_current_ki_temp_comp = conf_now->foc_current_ki * comp_fact;
	} else {
		motor->m_res_temp_comp = conf_now->foc_motor_r;
		motor->m_current_ki_temp_comp = conf_now->foc_current_ki;
	}

	// Check if it is time to stop the modulation. Notice that modulation is kept on as long as there is
	// field weakening current.
	utils_sys_lock_cnt();
	utils_step_towards((float*)&motor->m_current_off_delay, 0.0, dt);
	if (!motor->m_phase_override && motor->m_state == MC_STATE_RUNNING &&
			(motor->m_control_mode == CONTROL_MODE_CURRENT ||
					motor->m_control_mode == CONTROL_MODE_CURRENT_BRAKE ||
					motor->m_control_mode == CONTROL_MODE_HANDBRAKE ||
					motor->m_control_mode == CONTROL_MODE_OPENLOOP ||
					motor->m_control_mode == CONTROL_MODE_OPENLOOP_PHASE)) {

		// This is required to allow releasing the motor when cc_min_current is 0
		float min_current = conf_now->cc_min_current;
		if (min_current < 0.001 && get_motor_now()->m_motor_released) {
			min_current = 0.001;
		}

		if (fabsf(motor->m_iq_set) < min_current &&
				fabsf(motor->m_id_set) < min_current &&
				motor->m_i_fw_set < min_current &&
				motor->m_current_off_delay < dt) {
			motor->m_control_mode = CONTROL_MODE_NONE;
			motor->m_state = MC_STATE_OFF;
			stop_pwm_hw(motor);
		}
	}
	utils_sys_unlock_cnt();

	// Use this to study the openloop timers under experiment plot
#if 0
	{
		static bool plot_started = false;
		static int plot_div = 0;
		static float plot_int = 0.0;
		static int get_fw_version_cnt = 0;

		if (commands_get_fw_version_sent_cnt() != get_fw_version_cnt) {
			get_fw_version_cnt = commands_get_fw_version_sent_cnt();
			plot_started = false;
		}

		plot_div++;
		if (plot_div >= 10) {
			plot_div = 0;
			if (!plot_started) {
				plot_started = true;
				commands_init_plot("Time", "Val");
				commands_plot_add_graph("m_min_rpm_timer");
				commands_plot_add_graph("m_min_rpm_hyst_timer");
			}

			commands_plot_set_graph(0);
			commands_send_plot_points(plot_int, motor->m_min_rpm_timer);
			commands_plot_set_graph(1);
			commands_send_plot_points(plot_int, motor->m_min_rpm_hyst_timer);
			plot_int++;
		}
	}
#endif

	// Use this to study the observer state in a XY-plot
#if 0
	{
		static bool plot_started = false;
		static int plot_div = 0;
		static int get_fw_version_cnt = 0;

		if (commands_get_fw_version_sent_cnt() != get_fw_version_cnt) {
			get_fw_version_cnt = commands_get_fw_version_sent_cnt();
			plot_started = false;
		}

		plot_div++;
		if (plot_div >= 10) {
			plot_div = 0;
			if (!plot_started) {
				plot_started = true;
				commands_init_plot("X1", "X2");
				commands_plot_add_graph("Observer");
				commands_plot_add_graph("Observer Mag");
			}

			commands_plot_set_graph(0);
			commands_send_plot_points(m_motor_1.m_observer_x1, m_motor_1.m_observer_x2);
			float mag = NORM2_f(m_motor_1.m_observer_x1, m_motor_1.m_observer_x2);
			commands_plot_set_graph(1);
			commands_send_plot_points(0.0, mag);
		}
	}
#endif

	float t_lock = conf_now->foc_sl_openloop_time_lock;
	float t_ramp = conf_now->foc_sl_openloop_time_ramp;
	float t_const = conf_now->foc_sl_openloop_time;

	float openloop_current = fabsf(motor->m_motor_state.iq_filter);
	openloop_current += conf_now->foc_sl_openloop_boost_q;
	if (conf_now->foc_sl_openloop_max_q > 0.0) {
		utils_truncate_number(&openloop_current, 0.0, conf_now->foc_sl_openloop_max_q);
	}

	float openloop_rpm_max = utils_map(openloop_current,
			0.0, conf_now->l_current_max,
			conf_now->foc_openloop_rpm_low * conf_now->foc_openloop_rpm,
			conf_now->foc_openloop_rpm);

	utils_truncate_number_abs(&openloop_rpm_max, conf_now->foc_openloop_rpm);

	float openloop_rpm = openloop_rpm_max;
	if (conf_now->foc_sensor_mode != FOC_SENSOR_MODE_ENCODER) {
		float time_fwd = t_lock + t_ramp + t_const - motor->m_min_rpm_timer;
		if (time_fwd < t_lock) {
			openloop_rpm = 0.0;
		} else if (time_fwd < (t_lock + t_ramp)) {
			openloop_rpm = utils_map(time_fwd, t_lock,
					t_lock + t_ramp, 0.0, openloop_rpm);
		}
	}

	utils_truncate_number_abs(&openloop_rpm, openloop_rpm_max);

	float add_min_speed = 0.0;
	if (motor->m_motor_state.duty_now > 0.0) {
		add_min_speed = RPM2RADPS_f(openloop_rpm) * dt;
	} else {
		add_min_speed = -RPM2RADPS_f(openloop_rpm) * dt;
	}

	// Open loop encoder angle for when the index is not found
	motor->m_phase_now_encoder_no_index += add_min_speed;
	utils_norm_angle_rad((float*)&motor->m_phase_now_encoder_no_index);

	if (fabsf(motor->m_pll_speed) < RPM2RADPS_f(openloop_rpm_max) &&
			motor->m_min_rpm_hyst_timer < conf_now->foc_sl_openloop_hyst) {
		motor->m_min_rpm_hyst_timer += dt;
	} else if (motor->m_min_rpm_hyst_timer > 0.0) {
		motor->m_min_rpm_hyst_timer -= dt;
	}

	// Don't use this in brake mode.
	if (motor->m_control_mode == CONTROL_MODE_CURRENT_BRAKE ||
			(motor->m_state == MC_STATE_RUNNING && fabsf(motor->m_motor_state.duty_now) < 0.001)) {
		motor->m_min_rpm_hyst_timer = 0.0;
		motor->m_min_rpm_timer = 0.0;
		motor->m_phase_observer_override = false;
	}

	bool started_now = false;
	if (motor->m_min_rpm_hyst_timer >= conf_now->foc_sl_openloop_hyst &&
			motor->m_min_rpm_timer <= 0.0001) {
		motor->m_min_rpm_timer = t_lock + t_ramp + t_const;
		started_now = true;
	}

	if (motor->m_state != MC_STATE_RUNNING) {
		motor->m_min_rpm_timer = 0.0;
	}

	if (motor->m_min_rpm_timer > 0.0) {
		motor->m_phase_now_observer_override += add_min_speed;

		// When the motor gets stuck it tends to be 90 degrees off, so start the open loop
		// sequence by correcting with 60 degrees.
		if (started_now) {
			if (motor->m_motor_state.duty_now > 0.0) {
				motor->m_phase_now_observer_override += M_PI / 3.0;
			} else {
				motor->m_phase_now_observer_override -= M_PI / 3.0;
			}
		}

		utils_norm_angle_rad((float*)&motor->m_phase_now_observer_override);
		motor->m_phase_observer_override = true;
		motor->m_min_rpm_timer -= dt;
		motor->m_min_rpm_hyst_timer = 0.0;

		// Set observer state to help it start tracking when leaving open loop.
		float s, c;
		utils_fast_sincos_better(motor->m_phase_now_observer_override + SIGN(motor->m_motor_state.duty_now) * M_PI / 4.0, &s, &c);
		motor->m_observer_x1_override = c * conf_now->foc_motor_flux_linkage;
		motor->m_observer_x2_override = s * conf_now->foc_motor_flux_linkage;
	} else {
		motor->m_phase_now_observer_override = motor->m_phase_now_observer;
		motor->m_phase_observer_override = false;
	}

	// Samples
	if (motor->m_state == MC_STATE_RUNNING) {
		const volatile float vd_tmp = motor->m_motor_state.vd;
		const volatile float vq_tmp = motor->m_motor_state.vq;
		const volatile float id_tmp = motor->m_motor_state.id;
		const volatile float iq_tmp = motor->m_motor_state.iq;

		motor->m_samples.avg_current_tot += NORM2_f(id_tmp, iq_tmp);
		motor->m_samples.avg_voltage_tot += NORM2_f(vd_tmp, vq_tmp);
		motor->m_samples.sample_num++;
	}

	// Observer gain scaling, based on bus voltage and duty cycle
	float gamma_tmp = utils_map(fabsf(motor->m_motor_state.duty_now),
								0.0, 40.0 / motor->m_motor_state.v_bus,
								0, conf_now->foc_observer_gain);
	if (gamma_tmp < (conf_now->foc_observer_gain_slow * conf_now->foc_observer_gain)) {
		gamma_tmp = conf_now->foc_observer_gain_slow * conf_now->foc_observer_gain;
	}

	// 4.0 scaling is kind of arbitrary, but it should make configs from old VESC Tools more likely to work.
	motor->m_gamma_now = gamma_tmp * 4.0;

	// Run resistance observer
	// See "An adaptive flux observer for the permanent magnet synchronous motor"
	// https://doi.org/10.1002/acs.2587
	{
		float res_est_gain = 0.00002;
		float i_abs_sq = SQ(motor->m_motor_state.i_abs);
		motor->m_res_est = motor->m_r_est_state - 0.5 * res_est_gain * conf_now->foc_motor_l * i_abs_sq;
		float res_dot = -res_est_gain * (motor->m_res_est * i_abs_sq + motor->m_speed_est_fast *
				(motor->m_motor_state.i_beta * motor->m_observer_state.x1 - motor->m_motor_state.i_alpha * motor->m_observer_state.x2) -
				(motor->m_motor_state.i_alpha * motor->m_motor_state.v_alpha + motor->m_motor_state.i_beta * motor->m_motor_state.v_beta));
		motor->m_r_est_state += res_dot * dt;

		utils_truncate_number((float*)&motor->m_r_est_state, conf_now->foc_motor_r * 0.25, conf_now->foc_motor_r * 3.0);
	}
}

static void terminal_tmp(int argc, const char **argv) {
	(void)argc;
	(void)argv;

	int top = 1;
	if (argc == 2) {
		float seconds = -1.0;
		sscanf(argv[1], "%f", &seconds);

		if (seconds > 0.0) {
			top = seconds * 2;
		}
	}

	if (top > 1) {
		commands_init_plot("Time", "Temperature");
		commands_plot_add_graph("Temp Measured");
		commands_plot_add_graph("Temp Estimated");
		commands_plot_add_graph("lambda_est");
	}

	for (int i = 0;i < top;i++) {
		float res_est = m_motor_1.m_res_est;
		float t_base = m_motor_1.m_conf->foc_temp_comp_base_temp;
		float res_base = m_motor_1.m_conf->foc_motor_r;
		float t_est = (res_est / res_base - 1) / 0.00386 + t_base;
		float t_meas = mc_interface_temp_motor_filtered();

		if (top > 1) {
			commands_plot_set_graph(0);
			commands_send_plot_points((float)i / 2.0, t_meas);
			commands_plot_set_graph(1);
			commands_send_plot_points((float)i / 2.0, t_est);
			commands_plot_set_graph(2);
			commands_send_plot_points((float)i / 2.0, m_motor_1.m_observer_state.lambda_est);
			commands_printf("Sample %d of %d", i, top);
		}

		commands_printf("R: %.2f, EST: %.2f",
				(double)(res_base * 1000.0), (double)(res_est * 1000.0));
		commands_printf("T: %.2f, T_EST: %.2f\n",
				(double)t_meas, (double)t_est);

		chThdSleepMilliseconds(500);
	}
}

// TODO: This won't work for dual motors
static void input_current_offset_measurement(void) {
#ifdef HW_HAS_INPUT_CURRENT_SENSOR
	static uint16_t delay_current_offset_measurement = 0;

	if (delay_current_offset_measurement < 1000) {
		delay_current_offset_measurement++;
	} else {
		if (delay_current_offset_measurement == 1000) {
			delay_current_offset_measurement++;
			MEASURE_INPUT_CURRENT_OFFSET();
		}
	}
#endif
}

static THD_FUNCTION(timer_thread, arg) {
	(void)arg;

	chRegSetThreadName("foc timer");

	for(;;) {
		const float dt = 0.001;

		if (timer_thd_stop) {
			timer_thd_stop = false;
			return;
		}

		timer_update((motor_all_state_t*)&m_motor_1, dt);
#ifdef HW_HAS_DUAL_MOTORS
		timer_update((motor_all_state_t*)&m_motor_2, dt);
#endif

		input_current_offset_measurement();

		chThdSleepMilliseconds(1);
	}
}

/*
 * ========================================================================
 * 高频注入(HFI)算法更新函数 - 低速无传感器控制
 * ========================================================================
 *
 * 功能：通过注入高频信号检测转子位置，实现低速无传感器控制
 * 目的：在观测器失效的低速区域提供准确的位置反馈
 *
 * HFI原理：
 *
 * 1. 凸极效应：
 *    永磁同步电机具有凸极特性：Ld ≠ Lq
 *    电感随转子位置变化：L(θ) = L0 + ΔL×cos(2θ)
 *
 * 2. 高频注入：
 *    注入高频电压信号：v_hf = V_hf×cos(ωt)×[cos(θ_inj), sin(θ_inj)]
 *    产生高频电流响应：i_hf = v_hf / (jωL(θ))
 *
 * 3. 位置信息提取：
 *    高频电流包含位置信息：i_hf ∝ 1/L(θ) ∝ 1/(L0 + ΔL×cos(2θ))
 *    通过解调和滤波提取2倍频分量，获得位置信息
 *
 * 4. 角度计算：
 *    θ_rotor = 0.5 × atan2(I_2f_β, I_2f_α)
 *    其中I_2f_α, I_2f_β为2倍频电流分量
 *
 * HFI版本：
 * - HFI_V1: 基础版本，旋转注入
 * - HFI_V2/V3: 脉冲注入，±45°方向
 * - HFI_V4/V5: 改进的脉冲注入，d轴方向
 *
 * 适用条件：
 * - 低速运行（<300 ERPM）
 * - 凸极电机（Ld ≠ Lq）
 * - 足够的凸极比（通常>1.1）
 *
 * 性能指标：
 * - 位置精度：±2°
 * - 速度范围：0-500 ERPM
 * - 注入频率：1-10 kHz
 * - 注入电压：1-10V
 */
static void hfi_update(volatile motor_all_state_t *motor, float dt) {
	(void)dt;
	float rpm_abs = fabsf(RADPS2RPM_f(motor->m_speed_est_fast));

	/*
	 * 高速切换到观测器
	 *
	 * 当转速超过HFI阈值时，切换到观测器模式：
	 * - 观测器在高速时更准确
	 * - HFI在高速时信噪比下降
	 */
	if (rpm_abs > motor->m_conf->foc_sl_erpm_hfi) {
		motor->m_hfi.angle = motor->m_phase_now_observer;		// 使用观测器角度
		motor->m_hfi.double_integrator = -motor->m_speed_est_fast;	// 同步速度估计
	}

	if (motor->m_hfi.ready) {
		if ((motor->m_conf->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V4 ||
				motor->m_conf->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V5) &&
				motor->m_hfi.est_done_cnt >= motor->m_conf->foc_hfi_start_samples) {
			// Nothing done here, the update is done in the interrupt.
		} else if ((motor->m_conf->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V2 ||
				motor->m_conf->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V3) &&
				motor->m_hfi.est_done_cnt >= motor->m_conf->foc_hfi_start_samples) {
			// Nothing done here, the update is done in the interrupt.

			// Enable to set the observer position to the HFI angle for plotting the error in the position plot RT page in VESC Tool. Just
			// remember that enabling this will make the transition to using the observer bad.
#if 0
			float s, c;
			utils_fast_sincos_better(motor->m_hfi.angle, &s, &c);
			motor->m_observer_x1 = c * motor->m_conf->foc_motor_flux_linkage;
			motor->m_observer_x2 = s * motor->m_conf->foc_motor_flux_linkage;
#endif

			// Enable to plot the sample together with encoder position
#if 0

			commands_plot_set_graph(0);
			commands_send_plot_points(motor->m_hfi_plot_sample, ind_a);
			commands_plot_set_graph(1);
			commands_send_plot_points(motor->m_hfi_plot_sample, RAD2DEG_f(motor->m_phase_now_encoder) / 4e6);
			motor->m_hfi_plot_sample++;
#endif
		} else {
			/*
			 * ========================================================================
			 * HFI V1 - 基于FFT的旋转注入算法
			 * ========================================================================
			 *
			 * 算法原理：
			 * 1. 注入旋转高频电压矢量
			 * 2. 采样高频电流响应
			 * 3. 使用FFT提取特定频率分量
			 * 4. 从频域信息中解算转子位置
			 *
			 * FFT分析：
			 * - bin1: 基频分量（注入频率）
			 * - bin2: 2倍频分量（包含位置信息）
			 */

			// FFT计算：提取基频和2倍频分量
			float real_bin1, imag_bin1, real_bin2, imag_bin2;
			motor->m_hfi.fft_bin1_func((float*)motor->m_hfi.buffer, &real_bin1, &imag_bin1);	// 基频分量
			motor->m_hfi.fft_bin2_func((float*)motor->m_hfi.buffer, &real_bin2, &imag_bin2);	// 2倍频分量

			/*
			 * 基频分量处理（bin1）
			 *
			 * 基频分量主要反映电机的饱和特性，可用于：
			 * - 检测磁饱和程度
			 * - 估算电感变化
			 * - 辅助位置估计
			 */
			float mag_bin_1 = NORM2_f(imag_bin1, real_bin1);				// 基频幅值
			float angle_bin_1 = -utils_fast_atan2(imag_bin1, real_bin1);		// 基频相位

			// 相位校正：补偿系统延迟和硬件相移
			// 注：1.7这个系数是经验值，可能需要根据具体硬件调整
			angle_bin_1 += M_PI / 1.7; // TODO: 需要理论分析确定此系数
			utils_norm_angle_rad(&angle_bin_1);

			/*
			 * 2倍频分量处理（bin2）
			 *
			 * 2倍频分量包含转子位置信息：
			 * - 幅值：反映凸极程度 (Ld-Lq)
			 * - 相位：包含转子位置信息
			 *
			 * 位置计算：θ_rotor = angle_bin_2 / 2
			 */
			float mag_bin_2 = NORM2_f(imag_bin2, real_bin2);				// 2倍频幅值
			float angle_bin_2 = -utils_fast_atan2(imag_bin2, real_bin2) / 2.0;		// 转子位置（除以2）

			// Assuming this thread is much faster than it takes to fill the HFI buffer completely,
			// we should lag 1/2 HFI buffer behind in phase. Compensate for that here.
			float dt_sw;
			if (motor->m_conf->foc_sample_v0_v7) {
				dt_sw = 1.0 / motor->m_conf->foc_f_zv;
			} else {
				dt_sw = 1.0 / (motor->m_conf->foc_f_zv / 2.0);
			}
			angle_bin_2 += motor->m_motor_state.speed_rad_s * ((float)motor->m_hfi.samples / 2.0) * dt_sw;

			if (fabsf(utils_angle_difference_rad(angle_bin_2 + M_PI, motor->m_hfi.angle)) <
					fabsf(utils_angle_difference_rad(angle_bin_2, motor->m_hfi.angle))) {
				angle_bin_2 += M_PI;
			}

			if (motor->m_hfi.est_done_cnt < motor->m_conf->foc_hfi_start_samples) {
				motor->m_hfi.est_done_cnt++;

				if (fabsf(utils_angle_difference_rad(angle_bin_2, angle_bin_1)) > (M_PI / 2.0)) {
					motor->m_hfi.flip_cnt++;
				}
			}

			if (motor->m_hfi.est_done_cnt >= motor->m_conf->foc_hfi_start_samples) {
				if (motor->m_hfi.flip_cnt >= (motor->m_conf->foc_hfi_start_samples / 2)) {
					angle_bin_2 += M_PI;
				}
				motor->m_hfi.flip_cnt = 0;

				if (motor->m_conf->foc_sensor_mode == FOC_SENSOR_MODE_HFI_START) {
					float s, c;
					utils_fast_sincos_better(angle_bin_2, &s, &c);
					motor->m_observer_state.x1 = c * motor->m_conf->foc_motor_flux_linkage;
					motor->m_observer_state.x2 = s * motor->m_conf->foc_motor_flux_linkage;
				}
			}

			motor->m_hfi.angle = angle_bin_2;
			utils_norm_angle_rad((float*)&motor->m_hfi.angle);

			// As angle_bin_1 is based on saturation, it is only accurate when the motor current is low. It
			// might be possible to compensate for that, which would allow HFI on non-salient motors.
			//			m_hfi.angle = angle_bin_1;

			if (motor->m_hfi_plot_en == 1) {
				static float hfi_plot_div = 0;
				hfi_plot_div++;

				if (hfi_plot_div >= 8) {
					hfi_plot_div = 0;

					float real_bin0, imag_bin0;
					motor->m_hfi.fft_bin0_func((float*)motor->m_hfi.buffer, &real_bin0, &imag_bin0);

					commands_plot_set_graph(0);
					commands_send_plot_points(motor->m_hfi_plot_sample, motor->m_hfi.angle);

					commands_plot_set_graph(1);
					commands_send_plot_points(motor->m_hfi_plot_sample, angle_bin_1);

					commands_plot_set_graph(2);
					commands_send_plot_points(motor->m_hfi_plot_sample, 2.0 * mag_bin_2 * 1e6);

					commands_plot_set_graph(3);
					commands_send_plot_points(motor->m_hfi_plot_sample, 2.0 * mag_bin_1 * 1e6);

					commands_plot_set_graph(4);
					commands_send_plot_points(motor->m_hfi_plot_sample, real_bin0 * 1e6);

//					commands_plot_set_graph(0);
//					commands_send_plot_points(motor->m_hfi_plot_sample, motor->m_motor_state.speed_rad_s);
//
//					commands_plot_set_graph(1);
//					commands_send_plot_points(motor->m_hfi_plot_sample, motor->m_speed_est_fast);

					motor->m_hfi_plot_sample++;
				}
			} else if (motor->m_hfi_plot_en == 2) {
				static float hfi_plot_div = 0;
				hfi_plot_div++;

				if (hfi_plot_div >= 8) {
					hfi_plot_div = 0;

					if (motor->m_hfi_plot_sample >= motor->m_hfi.samples) {
						motor->m_hfi_plot_sample = 0;
					}

					commands_plot_set_graph(0);
					commands_send_plot_points(motor->m_hfi_plot_sample, motor->m_hfi.buffer_current[(int)motor->m_hfi_plot_sample]);

					commands_plot_set_graph(1);
					commands_send_plot_points(motor->m_hfi_plot_sample, motor->m_hfi.buffer[(int)motor->m_hfi_plot_sample] * 1e6);

					motor->m_hfi_plot_sample++;
				}
			}
		}
	} else {
		motor->m_hfi.angle = motor->m_phase_now_observer;
		motor->m_hfi.double_integrator = -motor->m_speed_est_fast;
	}
}

static THD_FUNCTION(hfi_thread, arg) {
	(void)arg;

	chRegSetThreadName("foc hfi");

	uint32_t t_last = timer_time_now();

	for(;;) {
		if (hfi_thd_stop) {
			hfi_thd_stop = false;
			return;
		}

		float dt = timer_seconds_elapsed_since(t_last);
		t_last = timer_time_now();

		hfi_update(&m_motor_1, dt);
#ifdef HW_HAS_DUAL_MOTORS
		hfi_update(&m_motor_2, dt);
#endif

		chThdSleepMicroseconds(500);
	}
}

static THD_FUNCTION(pid_thread, arg) {
	(void)arg;

	chRegSetThreadName("foc pid");

	uint32_t last_time = timer_time_now();

	for(;;) {
		if (pid_thd_stop) {
			pid_thd_stop = false;
			return;
		}

		switch (m_motor_1.m_conf->sp_pid_loop_rate) {
		case PID_RATE_25_HZ: chThdSleepMicroseconds(1000000 / 25); break;
		case PID_RATE_50_HZ: chThdSleepMicroseconds(1000000 / 50); break;
		case PID_RATE_100_HZ: chThdSleepMicroseconds(1000000 / 100); break;
		case PID_RATE_250_HZ: chThdSleepMicroseconds(1000000 / 250); break;
		case PID_RATE_500_HZ: chThdSleepMicroseconds(1000000 / 500); break;
		case PID_RATE_1000_HZ: chThdSleepMicroseconds(1000000 / 1000); break;
		case PID_RATE_2500_HZ: chThdSleepMicroseconds(1000000 / 2500); break;
		case PID_RATE_5000_HZ: chThdSleepMicroseconds(1000000 / 5000); break;
		case PID_RATE_10000_HZ: chThdSleepMicroseconds(1000000 / 10000); break;
		}

		float dt = timer_seconds_elapsed_since(last_time);
		last_time = timer_time_now();

		foc_run_pid_control_pos(encoder_index_found(), dt, (motor_all_state_t*)&m_motor_1);
		foc_run_pid_control_speed(dt, (motor_all_state_t*)&m_motor_1);
#ifdef HW_HAS_DUAL_MOTORS
		foc_run_pid_control_pos(encoder_index_found(), dt, (motor_all_state_t*)&m_motor_2);
		foc_run_pid_control_speed(dt, (motor_all_state_t*)&m_motor_2);
#endif
	}
}

/**
 * Run the current control loop.
 *
 * @param state_m
 * The motor state.
 *
 * Parameters that shall be set before calling this function:
 * id_target
 * iq_target
 * max_duty
 * phase
 * i_alpha
 * i_beta
 * v_bus
 * speed_rad_s
 *
 * Parameters that will be updated in this function:
 * i_bus
 * i_abs
 * i_abs_filter
 * v_alpha
 * v_beta
 * mod_d
 * mod_q
 * id
 * iq
 * id_filter
 * iq_filter
 * vd
 * vq
 * vd_int
 * vq_int
 * svm_sector
 *
 * @param dt
 * The time step in seconds.
 */
static void control_current(motor_all_state_t *motor, float dt) {
	volatile motor_state_t *state_m = &motor->m_motor_state;
	volatile mc_configuration *conf_now = motor->m_conf;

	float s = state_m->phase_sin;
	float c = state_m->phase_cos;

	float abs_rpm = fabsf(RADPS2RPM_f(motor->m_speed_est_fast));

	bool do_hfi = (conf_now->foc_sensor_mode == FOC_SENSOR_MODE_HFI ||
			conf_now->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V2 ||
			conf_now->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V3 ||
			conf_now->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V4 ||
			conf_now->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V5 ||
			(conf_now->foc_sensor_mode == FOC_SENSOR_MODE_HFI_START &&
					motor->m_control_mode != CONTROL_MODE_CURRENT_BRAKE &&
					fabsf(state_m->iq_target) > conf_now->cc_min_current)) &&
							!motor->m_phase_override &&
							abs_rpm < (conf_now->foc_sl_erpm_hfi * (motor->m_cc_was_hfi ? 1.8 : 1.5));

	bool hfi_est_done = motor->m_hfi.est_done_cnt >= conf_now->foc_hfi_start_samples;

	// Only allow Q axis current after the HFI ambiguity is resolved. This causes
	// a short delay when starting.
	if (do_hfi && !hfi_est_done) {
		state_m->iq_target = 0;
	} else if (conf_now->foc_sensor_mode == FOC_SENSOR_MODE_HFI_START) {
		do_hfi = false;
	}

	motor->m_cc_was_hfi = do_hfi;

	float max_duty = fabsf(state_m->max_duty);
	utils_truncate_number(&max_duty, 0.0, conf_now->l_max_duty);

	/*
	 * ========================================================================
	 * Park变换 (αβ → dq坐标变换) - FOC算法第二步
	 * ========================================================================
	 *
	 * 功能：将两相静止坐标系(αβ)转换为两相旋转坐标系(dq)
	 * 目的：将交流量转换为直流量，实现解耦控制
	 *
	 * 数学原理：
	 * Park变换是一个旋转坐标变换，将静止的αβ坐标系旋转θ角度，
	 * 使得新的坐标系与转子磁场同步旋转。
	 *
	 * 变换矩阵：
	 * [d]   [ cos(θ)  sin(θ)] [α]
	 * [q] = [-sin(θ)  cos(θ)] [β]
	 *
	 * 变换方程：
	 * d = α×cos(θ) + β×sin(θ)
	 * q = β×cos(θ) - α×sin(θ)
	 *
	 * 其中：
	 * - θ: 转子电角度 (rad)
	 * - c = cos(θ), s = sin(θ)
	 *
	 * 坐标系定义：
	 * - d轴：磁通轴，与转子磁场N极方向重合
	 * - q轴：转矩轴，超前d轴90°电角度
	 * - dq坐标系随转子同步旋转
	 *
	 * 物理意义：
	 * - id: d轴电流，控制磁通强度
	 *   * id > 0: 增磁，增强磁场
	 *   * id < 0: 弱磁，削弱磁场（高速运行）
	 *   * id = 0: 最大转矩电流比控制
	 *
	 * - iq: q轴电流，控制电磁转矩
	 *   * T = (3/2) × p × λpm × iq
	 *   * iq > 0: 正向转矩
	 *   * iq < 0: 反向转矩（制动）
	 *
	 * 控制优势：
	 * 1. 解耦控制：id和iq可以独立控制
	 * 2. 直流量控制：在dq坐标系中，正弦量变为直流量
	 * 3. 类似直流电机：转矩与电流成正比
	 * 4. 控制精度高：PI控制器对直流量控制效果好
	 */
	state_m->id = c * state_m->i_alpha + s * state_m->i_beta;	// d轴电流(磁通电流)
	state_m->iq = c * state_m->i_beta  - s * state_m->i_alpha;	// q轴电流(转矩电流)

	// Low passed currents are used for less time critical parts, not for the feedback
	UTILS_LP_FAST(state_m->id_filter, state_m->id, conf_now->foc_current_filter_const);
	UTILS_LP_FAST(state_m->iq_filter, state_m->iq, conf_now->foc_current_filter_const);

	float d_gain_scale = 1.0;
	if (conf_now->foc_d_gain_scale_start < 0.99) {
		float max_mod_norm = fabsf(state_m->duty_now / max_duty);
		if (max_duty < 0.01) {
			max_mod_norm = 1.0;
		}
		if (max_mod_norm > conf_now->foc_d_gain_scale_start) {
			d_gain_scale = utils_map(max_mod_norm, conf_now->foc_d_gain_scale_start, 1.0,
					1.0, conf_now->foc_d_gain_scale_max_mod);
			if (d_gain_scale < conf_now->foc_d_gain_scale_max_mod) {
				d_gain_scale = conf_now->foc_d_gain_scale_max_mod;
			}
		}
	}

	/*
	 * ========================================================================
	 * dq轴电流PI控制器 - FOC控制的核心
	 * ========================================================================
	 *
	 * 功能：实现dq坐标系中的解耦电流控制
	 * 目的：精确控制电机的磁通和转矩
	 *
	 * 控制原理：
	 * 在dq坐标系中，永磁同步电机的电流控制可以解耦为：
	 * - d轴电流控制：控制磁通，实现弱磁或增磁
	 * - q轴电流控制：控制转矩，直接影响电机输出
	 *
	 * PI控制器设计：
	 * 电机的电流传递函数为一阶系统：G(s) = 1/(L×s + R)
	 * 因此使用PI控制器即可获得良好的性能，无需微分项。
	 *
	 * 参数整定原则：
	 * - Kp = α × L：比例增益与电感成正比
	 * - Ki = α × R：积分增益与电阻成正比
	 * - α：调节系数，影响响应速度和稳定性
	 *
	 * 典型值：α = 2π × 1000 (对应1kHz带宽)
	 */

	// 计算电流误差
	float Ierr_d = state_m->id_target - state_m->id;		// d轴电流误差 (A)
	float Ierr_q = state_m->iq_target - state_m->iq;		// q轴电流误差 (A)

	/*
	 * 温度补偿
	 *
	 * 电机电阻随温度变化：R(T) = R₀ × [1 + α × (T - T₀)]
	 * 其中α为电阻温度系数，铜线约为0.004/°C
	 *
	 * 为保持控制性能，需要根据温度调整积分增益：
	 * Ki(T) = Ki₀ × R(T) / R₀
	 */
	float ki = conf_now->foc_current_ki;
	if (conf_now->foc_temp_comp) {
		ki = motor->m_current_ki_temp_comp;		// 使用温度补偿后的积分增益
	}

	/*
	 * 积分器更新
	 *
	 * 积分项累积：∫e(t)dt ≈ Σe(k)×dt
	 *
	 * d轴积分器使用增益缩放：
	 * - 在高调制度时降低d轴增益，提高系统稳定性
	 * - 防止d轴控制过于激进导致系统振荡
	 */
	state_m->vd_int += Ierr_d * (ki * d_gain_scale * dt);	// d轴积分器更新
	state_m->vq_int += Ierr_q * (ki * dt);					// q轴积分器更新

	/*
	 * PI电流控制器 (PI Current Controller)
	 *
	 * 在dq坐标系中实现解耦的电流控制。由于电机是一阶系统
	 * (传递函数 = 1/(Ls+R))，不需要微分(D)项。
	 *
	 * PI控制方程：
	 * u(k) = Kp×e(k) + Ki×∫e(k)dt
	 *
	 * 其中：
	 * - Kp: 比例增益，决定响应速度
	 * - Ki: 积分增益，消除稳态误差
	 * - e(k): 电流误差 = i_ref - i_actual
	 *
	 * d轴控制器(磁通控制)：
	 * - 使用d_gain_scale进行增益调整
	 * - 在高调制度时降低增益，提高稳定性
	 *
	 * q轴控制器(转矩控制)：
	 * - 直接控制电机转矩
	 * - 响应速度要求最高
	 */
	state_m->vd = state_m->vd_int + Ierr_d * conf_now->foc_current_kp * d_gain_scale;	// d轴PI控制器输出
	state_m->vq = state_m->vq_int + Ierr_q * conf_now->foc_current_kp;					// q轴PI控制器输出

	/*
	 * 解耦控制 (Decoupling Control)
	 *
	 * 使用前馈补偿来解决PMSM方程本质上不解耦的问题。
	 * d轴电流会影响q轴电压，反之亦然。
	 *
	 * PMSM电压方程：
	 *      电阻项    电感项      交叉耦合项    反电动势
	 * vd = Rs×id + Ld×did/dt - ωe×iq×Lq
	 * vq = Rs×iq + Lq×diq/dt + ωe×id×Ld + ωe×ψm
	 *
	 * 其中：
	 * - Rs: 定子电阻
	 * - Ld, Lq: d轴和q轴电感
	 * - ωe: 电角速度 = pole_pairs × ωm
	 * - ψm: 永磁体磁链
	 * - 交叉耦合项: -ωe×iq×Lq 和 +ωe×id×Ld
	 *
	 * 解耦的目的是消除交叉耦合项，使d轴和q轴控制独立。
	 *
	 * 参考：www.mathworks.com/help/physmod/sps/ref/pmsm.html
	 */
	float dec_vd = 0.0;
	float dec_vq = 0.0;
	float dec_bemf = 0.0;

	if (motor->m_control_mode < CONTROL_MODE_HANDBRAKE && conf_now->foc_cc_decoupling != FOC_CC_DECOUPLING_DISABLED) {
		switch (conf_now->foc_cc_decoupling) {
		case FOC_CC_DECOUPLING_CROSS:
			dec_vd = state_m->iq_filter * motor->m_speed_est_fast * motor->p_lq; // m_speed_est_fast is ωe in [rad/s]
			dec_vq = state_m->id_filter * motor->m_speed_est_fast * motor->p_ld;
			break;

		case FOC_CC_DECOUPLING_BEMF:
			dec_bemf = motor->m_speed_est_fast * conf_now->foc_motor_flux_linkage;
			break;

		case FOC_CC_DECOUPLING_CROSS_BEMF:
			dec_vd = state_m->iq_filter * motor->m_speed_est_fast * motor->p_lq;
			dec_vq = state_m->id_filter * motor->m_speed_est_fast * motor->p_ld;
			dec_bemf = motor->m_speed_est_fast * conf_now->foc_motor_flux_linkage;
			break;

		default:
			break;
		}
	}

	state_m->vd -= dec_vd; //Negative sign as in the PMSM equations
	state_m->vq += dec_vq + dec_bemf;

	/*
	 * 计算电压空间矢量最大长度(避免过调制)
	 *
	 * 在SVPWM中，为了避免过调制(overmodulation)，需要限制电压矢量的幅值。
	 *
	 * 理论推导：
	 * 在六边形电压空间中，最大可用电压矢量长度为：
	 * V_max = (1/√3) × V_bus
	 *
	 * 其中：
	 * - V_bus: 直流母线电压
	 * - 1/√3 ≈ 0.5774: 六边形内切圆半径与外接圆半径的比值
	 *
	 * 添加max_duty裕量以确保系统稳定性。
	 *
	 * 参考：https://microchipdeveloper.com/mct5001:start
	 */
	float max_v_mag = ONE_BY_SQRT3 * max_duty * state_m->v_bus;	// 最大电压矢量幅值

	/*
	 * 电压饱和限制和积分反饱和 (Voltage Saturation & Anti-Windup)
	 *
	 * 当PI控制器输出超过物理限制时，需要进行饱和限制，
	 * 同时防止积分器饱和(windup)。
	 *
	 * 优先级策略：
	 * d轴优先级高于q轴，因为d轴控制弱磁和效率。
	 *
	 * 饱和限制算法：
	 * 1. 首先限制vd在[-max_v_mag, +max_v_mag]范围内
	 * 2. 计算剩余电压空间：max_vq = √(max_v_mag² - vd²)
	 * 3. 限制vq在[-max_vq, +max_vq]范围内
	 *
	 * 积分反饱和：
	 * 当输出饱和时，调整积分器以防止积分饱和：
	 * I_new = I_old + (V_saturated - V_unsaturated)
	 */
	float vd_presat = state_m->vd;							// 饱和前的d轴电压
	utils_truncate_number_abs((float*)&state_m->vd, max_v_mag);		// d轴电压饱和限制
	state_m->vd_int += (state_m->vd - vd_presat);					// d轴积分器反饱和

	float max_vq = sqrtf(SQ(max_v_mag) - SQ(state_m->vd));			// 计算q轴最大可用电压
	float vq_presat = state_m->vq;							// 饱和前的q轴电压
	utils_truncate_number_abs((float*)&state_m->vq, max_vq);		// q轴电压饱和限制
	state_m->vq_int += (state_m->vq - vq_presat);					// q轴积分器反饱和

	utils_saturate_vector_2d((float*)&state_m->vd, (float*)&state_m->vq, max_v_mag);

	// mod_d and mod_q are normalized such that 1 corresponds to the max possible voltage:
	//    voltage_normalize = 1/(2/3*V_bus)
	// This includes overmodulation and therefore cannot be made in any direction.
	// Note that this scaling is different from max_v_mag, which is without over modulation.
	const float voltage_normalize = 1.5 / state_m->v_bus;
	state_m->mod_d = state_m->vd * voltage_normalize;
	state_m->mod_q = state_m->vq * voltage_normalize;
	UTILS_NAN_ZERO(state_m->mod_q_filter);
	UTILS_LP_FAST(state_m->mod_q_filter, state_m->mod_q, 0.2);

	// TODO: Have a look at this?
#ifdef HW_HAS_INPUT_CURRENT_SENSOR
	state_m->i_bus = GET_INPUT_CURRENT();
#else
	state_m->i_bus = state_m->mod_alpha_measured * state_m->i_alpha + state_m->mod_beta_measured * state_m->i_beta;
	// TODO: Also calculate motor power based on v_alpha, v_beta, i_alpha and i_beta. This is much more accurate
	// with phase filters than using the modulation and bus current.
#endif
	state_m->i_abs = NORM2_f(state_m->id, state_m->iq);
	state_m->i_abs_filter = NORM2_f(state_m->id_filter, state_m->iq_filter);

	/*
	 * ========================================================================
	 * 反Park变换 (dq → αβ坐标变换) - FOC算法第四步
	 * ========================================================================
	 *
	 * 功能：将两相旋转坐标系(dq)转换回两相静止坐标系(αβ)
	 * 目的：将PI控制器输出的直流控制量转换为交流调制信号
	 *
	 * 数学原理：
	 * 反Park变换是Park变换的逆变换，将旋转坐标系中的直流量
	 * 转换回静止坐标系中的交流量。
	 *
	 * 变换矩阵：
	 * [α]   [cos(θ)  -sin(θ)] [d]
	 * [β] = [sin(θ)   cos(θ)] [q]
	 *
	 * 变换方程：
	 * α = d×cos(θ) - q×sin(θ)
	 * β = d×sin(θ) + q×cos(θ)
	 *
	 * 其中：
	 * - θ: 转子电角度 (rad)
	 * - c = cos(θ), s = sin(θ)
	 * - mod_d, mod_q: dq轴调制信号(归一化电压)
	 * - mod_alpha_raw, mod_beta_raw: αβ轴调制信号
	 *
	 * 物理意义：
	 * - 将PI控制器计算的dq轴电压指令转换为αβ轴电压指令
	 * - 这些αβ轴调制信号将用于SVPWM算法生成三相PWM占空比
	 * - 实现了从直流控制量到交流输出的转换
	 *
	 * 信号流程：
	 * PI控制器输出 → dq电压 → 反Park变换 → αβ电压 → SVPWM → 三相PWM
	 *
	 * 注意事项：
	 * - 调制信号已经归一化，1对应最大可能电压
	 * - 包含过调制，因此不能在任意方向上实现
	 * - 电压归一化系数：voltage_normalize = 1.5/V_bus
	 */
	state_m->mod_alpha_raw = c * state_m->mod_d - s * state_m->mod_q;	// α轴调制信号
	state_m->mod_beta_raw  = c * state_m->mod_q + s * state_m->mod_d;	// β轴调制信号

	update_valpha_vbeta(motor, state_m->mod_alpha_raw, state_m->mod_beta_raw);

	// Dead time compensated values for vd and vq. Note that these are not used to control the switching times.
	state_m->vd = c * motor->m_motor_state.v_alpha + s * motor->m_motor_state.v_beta;
	state_m->vq = c * motor->m_motor_state.v_beta  - s * motor->m_motor_state.v_alpha;

	// HFI
	if (do_hfi) {
#ifdef HW_HAS_DUAL_MOTORS
		if (motor == &m_motor_2) {
			CURRENT_FILTER_OFF_M2();
		} else {
			CURRENT_FILTER_OFF();
		}
#else
		CURRENT_FILTER_OFF();
#endif

		float mod_alpha_v7 = state_m->mod_alpha_raw;
		float mod_beta_v7 = state_m->mod_beta_raw;

#ifdef HW_HAS_PHASE_SHUNTS
		float mod_alpha_v0 = state_m->mod_alpha_raw;
		float mod_beta_v0 = state_m->mod_beta_raw;
#endif

		float hfi_voltage;
		if (motor->m_hfi.est_done_cnt < conf_now->foc_hfi_start_samples) {
			hfi_voltage = conf_now->foc_hfi_voltage_start;
		} else {
			hfi_voltage = utils_map(fabsf(state_m->iq), -0.01, conf_now->l_current_max,
					conf_now->foc_hfi_voltage_run, conf_now->foc_hfi_voltage_max);
		}

		utils_truncate_number_abs(&hfi_voltage, state_m->v_bus * (1.0 - fabsf(state_m->duty_now)) * SQRT3_BY_2 * (2.0 / 3.0) * 0.95);

		if ((conf_now->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V4 || conf_now->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V5) && hfi_est_done) {
			if (motor->m_hfi.is_samp_n) {
				float sample_now = c * motor->m_i_beta_sample_with_offset - s * motor->m_i_alpha_sample_with_offset;
				float di = (motor->m_hfi.prev_sample - sample_now);

				if (!motor->m_using_encoder) {
					motor->m_hfi.double_integrator = -motor->m_speed_est_fast;
					motor->m_hfi.angle = motor->m_phase_now_observer;
				} else {
					float hfi_dt = dt * 2.0;
#ifdef HW_HAS_PHASE_SHUNTS
					if (!conf_now->foc_sample_v0_v7 && conf_now->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V4) {
						hfi_dt = dt;
					}
#endif
					foc_hfi_adjust_angle(
							(di * conf_now->foc_f_zv) / (hfi_voltage * motor->p_inv_ld_lq),
							motor, hfi_dt
					);
				}

#ifdef HW_HAS_PHASE_SHUNTS
				if (conf_now->foc_sample_v0_v7 || conf_now->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V5) {
					mod_alpha_v7 -= hfi_voltage * c * voltage_normalize;
					mod_beta_v7 -= hfi_voltage * s * voltage_normalize;
				} else {
					motor->m_hfi.prev_sample = c * motor->m_i_beta_sample_next - s * motor->m_i_alpha_sample_next;

					mod_alpha_v0 -= hfi_voltage * c * voltage_normalize;
					mod_beta_v0 -= hfi_voltage * s * voltage_normalize;

					mod_alpha_v7 += hfi_voltage * c * voltage_normalize;
					mod_beta_v7 += hfi_voltage * s * voltage_normalize;

					motor->m_hfi.is_samp_n = !motor->m_hfi.is_samp_n;
					motor->m_i_alpha_beta_has_offset = true;
				}
#else
				mod_alpha_v7 -= hfi_voltage * c * voltage_normalize;
				mod_beta_v7 -= hfi_voltage * s * voltage_normalize;
#endif
			} else {
				motor->m_hfi.prev_sample = c * state_m->i_beta - s * state_m->i_alpha;
				mod_alpha_v7 += hfi_voltage * c * voltage_normalize;
				mod_beta_v7  += hfi_voltage * s * voltage_normalize;
			}
		} else if ((conf_now->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V2 || conf_now->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V3) && hfi_est_done) {
			if (motor->m_hfi.is_samp_n) {
				if (fabsf(state_m->iq_target) > conf_now->foc_hfi_hyst) {
					motor->m_hfi.sign_last_sample = SIGN(state_m->iq_target);
				}

				float sample_now = motor->m_hfi.cos_last * motor->m_i_alpha_sample_with_offset +
						motor->m_hfi.sin_last * motor->m_i_beta_sample_with_offset;
				float di = (sample_now - motor->m_hfi.prev_sample);

				if (!motor->m_using_encoder) {
					motor->m_hfi.double_integrator = -motor->m_speed_est_fast;
					motor->m_hfi.angle = motor->m_phase_now_observer;
				} else {
					float hfi_dt = dt * 2.0;
#ifdef HW_HAS_PHASE_SHUNTS
					if (!conf_now->foc_sample_v0_v7 && conf_now->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V2) {
						hfi_dt = dt;
					}
#endif
					foc_hfi_adjust_angle(
							motor->m_hfi.sign_last_sample * ((conf_now->foc_f_zv * di) /
									hfi_voltage - motor->p_v2_v3_inv_avg_half) / motor->p_inv_ld_lq,
							motor, hfi_dt
					);
				}

				// Use precomputed rotation matrix
				if (motor->m_hfi.sign_last_sample > 0) {
					// +45 Degrees
					motor->m_hfi.sin_last = ONE_BY_SQRT2 * (c + s);
					motor->m_hfi.cos_last = ONE_BY_SQRT2 * (c - s);
				} else {
					// -45 Degrees
					motor->m_hfi.sin_last = ONE_BY_SQRT2 * (-c + s);
					motor->m_hfi.cos_last = ONE_BY_SQRT2 * (c + s);
				}

#ifdef HW_HAS_PHASE_SHUNTS
				if (conf_now->foc_sample_v0_v7 || conf_now->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V3) {
					mod_alpha_v7 += hfi_voltage * motor->m_hfi.cos_last * voltage_normalize;
					mod_beta_v7 += hfi_voltage * motor->m_hfi.sin_last * voltage_normalize;
				} else {
					motor->m_hfi.prev_sample = motor->m_hfi.cos_last * motor->m_i_alpha_sample_next +
							motor->m_hfi.sin_last * motor->m_i_beta_sample_next;

					mod_alpha_v0 += hfi_voltage * motor->m_hfi.cos_last * voltage_normalize;
					mod_beta_v0 += hfi_voltage * motor->m_hfi.sin_last * voltage_normalize;

					mod_alpha_v7 -= hfi_voltage * motor->m_hfi.cos_last * voltage_normalize;
					mod_beta_v7 -= hfi_voltage * motor->m_hfi.sin_last * voltage_normalize;

					motor->m_hfi.is_samp_n = !motor->m_hfi.is_samp_n;
					motor->m_i_alpha_beta_has_offset = true;
				}
#else
				mod_alpha_v7 += hfi_voltage * motor->m_hfi.cos_last * voltage_normalize;
				mod_beta_v7 += hfi_voltage * motor->m_hfi.sin_last * voltage_normalize;
#endif
			} else {
				motor->m_hfi.prev_sample = motor->m_hfi.cos_last * state_m->i_alpha + motor->m_hfi.sin_last * state_m->i_beta;
				mod_alpha_v7 -= hfi_voltage * motor->m_hfi.cos_last * voltage_normalize;
				mod_beta_v7  -= hfi_voltage * motor->m_hfi.sin_last * voltage_normalize;
			}
		} else {
			if (motor->m_hfi.is_samp_n) {
				float sample_now = (utils_tab_sin_32_1[motor->m_hfi.ind * motor->m_hfi.table_fact] * state_m->i_alpha -
						utils_tab_cos_32_1[motor->m_hfi.ind * motor->m_hfi.table_fact] * state_m->i_beta);
				float di = (sample_now - motor->m_hfi.prev_sample);

				motor->m_hfi.buffer_current[motor->m_hfi.ind] = di;

				if (di > 0.01) {
					motor->m_hfi.buffer[motor->m_hfi.ind] = hfi_voltage / (conf_now->foc_f_zv * di);
				}

				motor->m_hfi.ind++;
				if (motor->m_hfi.ind == motor->m_hfi.samples) {
					motor->m_hfi.ind = 0;
					motor->m_hfi.ready = true;
				}

				mod_alpha_v7 += hfi_voltage * utils_tab_sin_32_1[motor->m_hfi.ind * motor->m_hfi.table_fact] * voltage_normalize;
				mod_beta_v7  -= hfi_voltage * utils_tab_cos_32_1[motor->m_hfi.ind * motor->m_hfi.table_fact] * voltage_normalize;
			} else {
				motor->m_hfi.prev_sample = utils_tab_sin_32_1[motor->m_hfi.ind * motor->m_hfi.table_fact] * state_m->i_alpha -
						utils_tab_cos_32_1[motor->m_hfi.ind * motor->m_hfi.table_fact] * state_m->i_beta;

				mod_alpha_v7 -= hfi_voltage * utils_tab_sin_32_1[motor->m_hfi.ind * motor->m_hfi.table_fact] * voltage_normalize;
				mod_beta_v7  += hfi_voltage * utils_tab_cos_32_1[motor->m_hfi.ind * motor->m_hfi.table_fact] * voltage_normalize;
			}
		}

		utils_saturate_vector_2d(&mod_alpha_v7, &mod_beta_v7, SQRT3_BY_2 * 0.95);
		motor->m_hfi.is_samp_n = !motor->m_hfi.is_samp_n;

		if (conf_now->foc_sample_v0_v7) {
			state_m->mod_alpha_raw = mod_alpha_v7;
			state_m->mod_beta_raw = mod_beta_v7;
		} else {
#ifdef HW_HAS_PHASE_SHUNTS
			if (conf_now->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V2 || conf_now->foc_sensor_mode == FOC_SENSOR_MODE_HFI_V4) {
				utils_saturate_vector_2d(&mod_alpha_v0, &mod_beta_v0, SQRT3_BY_2 * 0.95);
				state_m->mod_alpha_raw = mod_alpha_v0;
				state_m->mod_beta_raw = mod_beta_v0;
			}
#endif

			// Delay adding the HFI voltage when not sampling in both 0 vectors, as it will cancel
			// itself with the opposite pulse from the previous HFI sample. This makes more sense
			// when drawing the SVM waveform.
			foc_svm(mod_alpha_v7, mod_beta_v7, TIM1->ARR,
				(uint32_t*)&motor->m_duty1_next,
				(uint32_t*)&motor->m_duty2_next,
				(uint32_t*)&motor->m_duty3_next,
				(uint32_t*)&state_m->svm_sector);
			motor->m_duty_next_set = true;
		}
	} else {
#ifdef HW_HAS_DUAL_MOTORS
		if (motor == &m_motor_2) {
			CURRENT_FILTER_ON_M2();
		} else {
			CURRENT_FILTER_ON();
		}
#else
		CURRENT_FILTER_ON();
#endif
		motor->m_hfi.ind = 0;
		motor->m_hfi.ready = false;
		motor->m_hfi.is_samp_n = false;
		motor->m_hfi.prev_sample = 0.0;
		motor->m_hfi.double_integrator = 0.0;
	}

	// Set output (HW Dependent)
	uint32_t duty1, duty2, duty3, top;
	top = TIM1->ARR;

	// Calculate the duty cycles for all the phases. This also injects a zero modulation signal to
	// be able to fully utilize the bus voltage. See https://microchipdeveloper.com/mct5001:start
	foc_svm(state_m->mod_alpha_raw, state_m->mod_beta_raw, top, &duty1, &duty2, &duty3, (uint32_t*)&state_m->svm_sector);

	if (motor == &m_motor_1) {
		TIMER_UPDATE_DUTY_M1(duty1, duty2, duty3);
#ifdef HW_HAS_DUAL_PARALLEL
		TIMER_UPDATE_DUTY_M2(duty1, duty2, duty3);
#endif
	} else {
#ifndef HW_HAS_DUAL_PARALLEL
		TIMER_UPDATE_DUTY_M2(duty1, duty2, duty3);
#endif
	}

	// do not allow to turn on PWM outputs if virtual motor is used
	if(virtual_motor_is_connected() == false) {
		if (!motor->m_output_on) {
			start_pwm_hw(motor);
		}
	}
}

static void update_valpha_vbeta(motor_all_state_t *motor, float mod_alpha, float mod_beta) {
	motor_state_t *state_m = &motor->m_motor_state;
	mc_configuration *conf_now = motor->m_conf;
	float Va, Vb, Vc;

	volatile float *ofs_volt = conf_now->foc_offsets_voltage_undriven;
	if (motor->m_state == MC_STATE_RUNNING) {
		ofs_volt = conf_now->foc_offsets_voltage;
	}

#ifdef HW_HAS_DUAL_MOTORS
#ifdef HW_HAS_3_SHUNTS
	if (&m_motor_1 != motor) {
		Va = (ADC_VOLTS(ADC_IND_SENS4) - ofs_volt[0]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
		Vb = (ADC_VOLTS(ADC_IND_SENS5) - ofs_volt[1]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
		Vc = (ADC_VOLTS(ADC_IND_SENS6) - ofs_volt[2]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
	} else {
		Va = (ADC_VOLTS(ADC_IND_SENS1) - ofs_volt[0]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
		Vb = (ADC_VOLTS(ADC_IND_SENS2) - ofs_volt[1]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
		Vc = (ADC_VOLTS(ADC_IND_SENS3) - ofs_volt[2]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
	}
#else
	if (&m_motor_1 != motor) {
		Va = (ADC_VOLTS(ADC_IND_SENS4) - ofs_volt[0]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
		Vb = (ADC_VOLTS(ADC_IND_SENS6) - ofs_volt[2]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
		Vc = (ADC_VOLTS(ADC_IND_SENS5) - ofs_volt[1]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
	} else {
		Va = (ADC_VOLTS(ADC_IND_SENS1) - ofs_volt[0]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
		Vb = (ADC_VOLTS(ADC_IND_SENS3) - ofs_volt[2]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
		Vc = (ADC_VOLTS(ADC_IND_SENS2) - ofs_volt[1]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
	}
#endif
#else
#ifdef HW_HAS_3_SHUNTS
	Va = (ADC_VOLTS(ADC_IND_SENS1) - ofs_volt[0]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
	Vb = (ADC_VOLTS(ADC_IND_SENS2) - ofs_volt[1]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
	Vc = (ADC_VOLTS(ADC_IND_SENS3) - ofs_volt[2]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
#else
	Va = (ADC_VOLTS(ADC_IND_SENS1) - ofs_volt[0]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
	Vb = (ADC_VOLTS(ADC_IND_SENS3) - ofs_volt[2]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
	Vc = (ADC_VOLTS(ADC_IND_SENS2) - ofs_volt[1]) * ((VIN_R1 + VIN_R2) / VIN_R2) * ADC_VOLTS_PH_FACTOR;
#endif
#endif

	// Deadtime compensation
	float s = state_m->phase_sin;
	float c = state_m->phase_cos;
	const float i_alpha_filter = c * state_m->id_filter - s * state_m->iq_filter;
	const float i_beta_filter = c * state_m->iq_filter + s * state_m->id_filter;
	const float ia_filter = i_alpha_filter;
	const float ib_filter = -0.5 * i_alpha_filter + SQRT3_BY_2 * i_beta_filter;
	const float ic_filter = -0.5 * i_alpha_filter - SQRT3_BY_2 * i_beta_filter;

	// mod_alpha_sign = 2/3*sign(ia) - 1/3*sign(ib) - 1/3*sign(ic)
	// mod_beta_sign  = 1/sqrt(3)*sign(ib) - 1/sqrt(3)*sign(ic)
	const float mod_alpha_filter_sgn = (1.0 / 3.0) * (2.0 * SIGN(ia_filter) - SIGN(ib_filter) - SIGN(ic_filter));
	const float mod_beta_filter_sgn = ONE_BY_SQRT3 * (SIGN(ib_filter) - SIGN(ic_filter));

	const float mod_comp_fact = conf_now->foc_dt_us * 1e-6 * conf_now->foc_f_zv;
	const float mod_alpha_comp = mod_alpha_filter_sgn * mod_comp_fact;
	const float mod_beta_comp = mod_beta_filter_sgn * mod_comp_fact;

	mod_alpha -= mod_alpha_comp;
	mod_beta -= mod_beta_comp;

	state_m->va = Va;
	state_m->vb = Vb;
	state_m->vc = Vc;
	state_m->mod_alpha_measured = mod_alpha;
	state_m->mod_beta_measured = mod_beta;

	// v_alpha = 2/3*Va - 1/3*Vb - 1/3*Vc
	// v_beta  = 1/sqrt(3)*Vb - 1/sqrt(3)*Vc
	float v_alpha = (1.0 / 3.0) * (2.0 * Va - Vb - Vc);
	float v_beta = ONE_BY_SQRT3 * (Vb - Vc);

	// Keep the modulation updated so that the filter stays updated
	// even when the motor is undriven.
	if (motor->m_state != MC_STATE_RUNNING) {
		/* voltage_normalize = 1/(2/3*V_bus) */
		const float voltage_normalize = 1.5 / state_m->v_bus;

		mod_alpha = v_alpha * voltage_normalize;
		mod_beta = v_beta * voltage_normalize;
	}

	float abs_rpm = fabsf(RADPS2RPM_f(motor->m_speed_est_fast));

	float filter_const = 1.0;
	if (abs_rpm < 10000.0) {
		filter_const = utils_map(abs_rpm, 0.0, 10000.0, 0.01, 1.0);
	}

	float v_mag = NORM2_f(v_alpha, v_beta);
	// The 0.1 * v_mag term below compensates for the filter attenuation as the speed increases.
	// It is chosen by trial and error, so this can be improved.
	UTILS_LP_FAST(state_m->v_mag_filter, v_mag + 0.1 * v_mag * filter_const, filter_const);
	UTILS_LP_FAST(state_m->mod_alpha_filter, mod_alpha, filter_const);
	UTILS_LP_FAST(state_m->mod_beta_filter, mod_beta, filter_const);
	UTILS_NAN_ZERO(state_m->v_mag_filter);
	UTILS_NAN_ZERO(state_m->mod_alpha_filter);
	UTILS_NAN_ZERO(state_m->mod_beta_filter);

	mod_alpha = state_m->mod_alpha_filter;
	mod_beta = state_m->mod_beta_filter;

	if (motor->m_state == MC_STATE_RUNNING) {
#ifdef HW_HAS_PHASE_FILTERS
		if (conf_now->foc_phase_filter_enable && abs_rpm < conf_now->foc_phase_filter_max_erpm) {
			float mod_mag = NORM2_f(mod_alpha, mod_beta);
			float v_mag_mod = mod_mag * (2.0 / 3.0) * state_m->v_bus;

			if (!conf_now->foc_phase_filter_disable_fault && fabsf(v_mag_mod - state_m->v_mag_filter) > (conf_now->l_max_vin * 0.05)) {
				mc_interface_set_fault_info("v_mag_mod: %.2f, v_mag_filter: %.2f", 2, v_mag_mod, state_m->v_mag_filter);
				mc_interface_fault_stop(FAULT_CODE_PHASE_FILTER, &m_motor_1 != motor, true);
			}

			// Compensate for the phase delay by using the direction of the modulation
			// together with the magnitude from the phase filters
			if (mod_mag > 0.04) {
				state_m->v_alpha = mod_alpha / mod_mag * state_m->v_mag_filter;
				state_m->v_beta = mod_beta / mod_mag * state_m->v_mag_filter;
			} else {
				state_m->v_alpha = v_alpha;
				state_m->v_beta = v_beta;
			}

			state_m->is_using_phase_filters = true;
		} else {
#endif
			state_m->v_alpha = mod_alpha * (2.0 / 3.0) * state_m->v_bus;
			state_m->v_beta = mod_beta * (2.0 / 3.0) * state_m->v_bus;
			state_m->is_using_phase_filters = false;
#ifdef HW_HAS_PHASE_FILTERS
		}
#endif
	} else {
		state_m->v_alpha = v_alpha;
		state_m->v_beta = v_beta;
		state_m->is_using_phase_filters = false;

#ifdef HW_USE_LINE_TO_LINE
		// rotate alpha-beta 30 degrees to compensate for line-to-line phase voltage sensing
		float x_tmp = state_m->v_alpha;
		float y_tmp = state_m->v_beta;

		state_m->v_alpha = x_tmp * COS_MINUS_30_DEG - y_tmp * SIN_MINUS_30_DEG;
		state_m->v_beta = x_tmp * SIN_MINUS_30_DEG + y_tmp * COS_MINUS_30_DEG;

		// compensate voltage amplitude
		state_m->v_alpha *= ONE_BY_SQRT3;
		state_m->v_beta *= ONE_BY_SQRT3;
#endif
	}
}

static void stop_pwm_hw(motor_all_state_t *motor) {
	motor->m_id_set = 0.0;
	motor->m_iq_set = 0.0;

	if (motor == &m_motor_1) {
		TIM_SelectOCxM(TIM1, TIM_Channel_1, TIM_ForcedAction_InActive);
		TIM_CCxCmd(TIM1, TIM_Channel_1, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM1, TIM_Channel_1, TIM_CCxN_Disable);

		TIM_SelectOCxM(TIM1, TIM_Channel_2, TIM_ForcedAction_InActive);
		TIM_CCxCmd(TIM1, TIM_Channel_2, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM1, TIM_Channel_2, TIM_CCxN_Disable);

		TIM_SelectOCxM(TIM1, TIM_Channel_3, TIM_ForcedAction_InActive);
		TIM_CCxCmd(TIM1, TIM_Channel_3, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM1, TIM_Channel_3, TIM_CCxN_Disable);

		TIM_GenerateEvent(TIM1, TIM_EventSource_COM);

#ifdef HW_HAS_DUAL_PARALLEL
		TIM_SelectOCxM(TIM8, TIM_Channel_1, TIM_ForcedAction_InActive);
		TIM_CCxCmd(TIM8, TIM_Channel_1, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM8, TIM_Channel_1, TIM_CCxN_Disable);

		TIM_SelectOCxM(TIM8, TIM_Channel_2, TIM_ForcedAction_InActive);
		TIM_CCxCmd(TIM8, TIM_Channel_2, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM8, TIM_Channel_2, TIM_CCxN_Disable);

		TIM_SelectOCxM(TIM8, TIM_Channel_3, TIM_ForcedAction_InActive);
		TIM_CCxCmd(TIM8, TIM_Channel_3, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM8, TIM_Channel_3, TIM_CCxN_Disable);

		TIM_GenerateEvent(TIM8, TIM_EventSource_COM);
#endif

#ifdef HW_HAS_DRV8313
		DISABLE_BR();
#endif

		motor->m_output_on = false;
		PHASE_FILTER_OFF();
	} else {
		TIM_SelectOCxM(TIM8, TIM_Channel_1, TIM_ForcedAction_InActive);
		TIM_CCxCmd(TIM8, TIM_Channel_1, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM8, TIM_Channel_1, TIM_CCxN_Disable);

		TIM_SelectOCxM(TIM8, TIM_Channel_2, TIM_ForcedAction_InActive);
		TIM_CCxCmd(TIM8, TIM_Channel_2, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM8, TIM_Channel_2, TIM_CCxN_Disable);

		TIM_SelectOCxM(TIM8, TIM_Channel_3, TIM_ForcedAction_InActive);
		TIM_CCxCmd(TIM8, TIM_Channel_3, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM8, TIM_Channel_3, TIM_CCxN_Disable);

		TIM_GenerateEvent(TIM8, TIM_EventSource_COM);

#ifdef HW_HAS_DRV8313_2
		DISABLE_BR_2();
#endif

		motor->m_output_on = false;
		PHASE_FILTER_OFF_M2();
	}
}

static void start_pwm_hw(motor_all_state_t *motor) {
	if (motor == &m_motor_1) {
		TIM_SelectOCxM(TIM1, TIM_Channel_1, TIM_OCMode_PWM1);
		TIM_CCxCmd(TIM1, TIM_Channel_1, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM1, TIM_Channel_1, TIM_CCxN_Enable);

		TIM_SelectOCxM(TIM1, TIM_Channel_2, TIM_OCMode_PWM1);
		TIM_CCxCmd(TIM1, TIM_Channel_2, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM1, TIM_Channel_2, TIM_CCxN_Enable);

		TIM_SelectOCxM(TIM1, TIM_Channel_3, TIM_OCMode_PWM1);
		TIM_CCxCmd(TIM1, TIM_Channel_3, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM1, TIM_Channel_3, TIM_CCxN_Enable);

#ifdef HW_HAS_DUAL_PARALLEL
		TIM_SelectOCxM(TIM8, TIM_Channel_1, TIM_OCMode_PWM1);
		TIM_CCxCmd(TIM8, TIM_Channel_1, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM8, TIM_Channel_1, TIM_CCxN_Enable);

		TIM_SelectOCxM(TIM8, TIM_Channel_2, TIM_OCMode_PWM1);
		TIM_CCxCmd(TIM8, TIM_Channel_2, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM8, TIM_Channel_2, TIM_CCxN_Enable);

		TIM_SelectOCxM(TIM8, TIM_Channel_3, TIM_OCMode_PWM1);
		TIM_CCxCmd(TIM8, TIM_Channel_3, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM8, TIM_Channel_3, TIM_CCxN_Enable);

		PHASE_FILTER_ON_M2();
#endif

		// Generate COM event in ADC interrupt to get better synchronization
		//	TIM_GenerateEvent(TIM1, TIM_EventSource_COM);

#ifdef HW_HAS_DRV8313
		ENABLE_BR();
#endif
		motor->m_output_on = true;
		PHASE_FILTER_ON();
	} else {
		TIM_SelectOCxM(TIM8, TIM_Channel_1, TIM_OCMode_PWM1);
		TIM_CCxCmd(TIM8, TIM_Channel_1, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM8, TIM_Channel_1, TIM_CCxN_Enable);

		TIM_SelectOCxM(TIM8, TIM_Channel_2, TIM_OCMode_PWM1);
		TIM_CCxCmd(TIM8, TIM_Channel_2, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM8, TIM_Channel_2, TIM_CCxN_Enable);

		TIM_SelectOCxM(TIM8, TIM_Channel_3, TIM_OCMode_PWM1);
		TIM_CCxCmd(TIM8, TIM_Channel_3, TIM_CCx_Enable);
		TIM_CCxNCmd(TIM8, TIM_Channel_3, TIM_CCxN_Enable);

#ifdef HW_HAS_DRV8313_2
		ENABLE_BR_2();
#endif
		motor->m_output_on = true;
		PHASE_FILTER_ON_M2();
	}
}

static void terminal_plot_hfi(int argc, const char **argv) {
	if (argc == 2) {
		int d = -1;
		sscanf(argv[1], "%d", &d);

		if (d == 0 || d == 1 || d == 2 || d == 3) {
			get_motor_now()->m_hfi_plot_en = d;
			if (get_motor_now()->m_hfi_plot_en == 1) {
				get_motor_now()->m_hfi_plot_sample = 0.0;
				commands_init_plot("Sample", "Value");
				commands_plot_add_graph("Phase");
				commands_plot_add_graph("Phase bin2");
				commands_plot_add_graph("Ld - Lq (uH");
				commands_plot_add_graph("L Diff Sat (uH)");
				commands_plot_add_graph("L Avg (uH)");
			} else if (get_motor_now()->m_hfi_plot_en == 2) {
				get_motor_now()->m_hfi_plot_sample = 0.0;
				commands_init_plot("Sample Index", "Value");
				commands_plot_add_graph("Current (A)");
				commands_plot_add_graph("Inductance (uH)");
			} else if (get_motor_now()->m_hfi_plot_en == 3) {
				get_motor_now()->m_hfi_plot_sample = 0.0;
				commands_init_plot("Sample Index", "Value");
				commands_plot_add_graph("Inductance");;
			}

			commands_printf(get_motor_now()->m_hfi_plot_en ?
					"HFI plot enabled" :
					"HFI plot disabled");
		} else {
			commands_printf("Invalid Argument. en has to be 0, 1, 2 or 3.\n");
		}
	} else {
		commands_printf("This command requires one argument.\n");
	}
}
