# Motor控制模块Makefile配置文件
#
# 本文件定义了VESC电机控制模块的编译配置，包含了所有电机控制相关的
# 源文件和头文件路径。该模块是VESC固件的核心部分，实现了完整的
# 电机控制功能。
#
# 包含的源文件模块：
# - foc_math.c: FOC数学算法库
# - gpdrive.c: GPDrive栅极驱动器
# - mc_interface.c: 电机控制统一接口
# - mcpwm.c: BLDC电机PWM控制
# - mcpwm_foc.c: FOC电机PWM控制
# - virtual_motor.c: 虚拟电机仿真

CSRC += \
	motor/foc_math.c \
	motor/gpdrive.c \
	motor/mc_interface.c \
	motor/mcpwm.c \
	motor/mcpwm_foc.c \
	motor/virtual_motor.c

INCDIR += motor

