/*
	Copyright 2018 <PERSON>	<EMAIL>

	This file is part of the VESC firmware.

	The VESC firmware is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    The VESC firmware is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <http://www.gnu.org/licenses/>.
    */

/*
 * GPDrive驱动头文件
 *
 * 本文件定义了GPDrive(Gate Pre-Driver)栅极预驱动器的接口函数。
 * GPDrive是VESC硬件中负责驱动功率MOSFET栅极的专用芯片，
 * 提供高速、高精度的PWM信号生成和电流采样功能。
 *
 * 主要功能模块：
 * 1. 初始化和配置管理 - GPDrive芯片的初始化和参数配置
 * 2. PWM信号生成 - 高精度三相PWM波形输出
 * 3. 电流采样 - 同步电流采样和数据处理
 * 4. 频率控制 - 动态开关频率调节
 * 5. 缓冲区管理 - 采样数据缓冲和传输
 * 6. 状态监控 - 芯片工作状态和故障检测
 */

#ifndef GPDRIVE_H_
#define GPDRIVE_H_

#include "conf_general.h"

// Functions
void gpdrive_init(volatile mc_configuration *configuration);
void gpdrive_deinit(void);
bool gpdrive_init_done(void);
bool gpdrive_is_dccal_done(void);
float gpdrive_get_switching_frequency_now(void);
void gpdrive_set_configuration(volatile mc_configuration *configuration);
void gpdrive_output_sample(float sample);
void gpdrive_fill_buffer(float *samples, int sample_num);
void gpdrive_add_buffer_sample(float sample);
void gpdrive_add_buffer_sample_int(int sample);
void gpdrive_set_buffer_int_scale(float scale);
void gpdrive_set_switching_frequency(float freq);
int gpdrive_buffer_size_left(void);
void gpdrive_set_mode(gpd_output_mode mode);
float gpdrive_get_current(void);
float gpdrive_get_current_filtered(void);
float gpdrive_get_modulation(void);
float gpdrive_get_last_adc_isr_duration(void);

#endif /* GPDRIVE_H_ */
