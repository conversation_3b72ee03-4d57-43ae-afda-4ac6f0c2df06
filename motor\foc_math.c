/*
	Copyright 2016 - 2022 <PERSON>	<EMAIL>

	This file is part of the VESC firmware.

	The VESC firmware is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    The VESC firmware is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

/*
 * FOC数学库实现文件
 *
 * 本文件实现了磁场定向控制(FOC)的核心数学算法，包括：
 * 1. 无传感器观测器算法 - 用于估计转子位置和速度
 * 2. 空间矢量调制(SVM) - 高效的PWM生成
 * 3. PID控制器 - 位置和速度闭环控制
 * 4. 传感器融合算法 - 编码器和霍尔传感器处理
 * 5. 弱磁控制 - 扩展高速运行范围
 * 6. 高频注入(HFI) - 低速无传感器控制
 */

#include "foc_math.h"
#include "utils_math.h"
#include <math.h>

/**
 * FOC观测器更新函数
 *
 * 实现多种无传感器观测器算法，用于估计永磁同步电机的转子位置。
 *
 * 参考文献：
 * http://cas.ensmp.fr/~praly/Telechargement/Journaux/2010-IEEE_TPEL-Lee-Hong-Nam-Ortega-Praly-Astolfi.pdf
 *
 * 观测器基本原理：
 * 基于电机的数学模型：
 * dψ/dt = v - R*i
 * 其中 ψ = L*i + λ*[cos(θ), sin(θ)]^T
 *
 * 通过观测磁链矢量 ψ = [x1, x2]^T，可以得到转子位置：
 * θ = atan2(x2 - L*i_β, x1 - L*i_α)
 */
// See http://cas.ensmp.fr/~praly/Telechargement/Journaux/2010-IEEE_TPEL-Lee-Hong-Nam-Ortega-Praly-Astolfi.pdf
void foc_observer_update(float v_alpha, float v_beta, float i_alpha, float i_beta,
		float dt, observer_state *state, float *phase, motor_all_state_t *motor) {

	mc_configuration *conf_now = motor->m_conf;

	// 获取电机基本参数
	float R = conf_now->foc_motor_r;				// 定子电阻 (Ω)
	float L = conf_now->foc_motor_l;				// 定子电感 (H)
	float lambda = conf_now->foc_motor_flux_linkage;// 永磁体磁链 (Wb)

	// 饱和补偿
	// 当电机工作在大电流时，铁芯饱和会导致电感和磁链下降
	// 需要根据当前电流大小对参数进行补偿
	switch(conf_now->foc_sat_comp_mode) {
	case SAT_COMP_LAMBDA:
		// 磁链补偿模式：假设电感下降幅度与磁链相同
		// 这是一个经验假设，实际效果需要根据具体电机调整
		if (conf_now->foc_observer_type >= FOC_OBSERVER_ORTEGA_LAMBDA_COMP) {
			L = L * (state->lambda_est / lambda);
		}
		break;

	case SAT_COMP_FACTOR: {
		// 因子补偿模式：根据电流与额定电流的比值进行线性补偿
		// 补偿公式：L_comp = L * (1 - k * I/I_max)
		const float comp_fact = conf_now->foc_sat_comp * (motor->m_motor_state.i_abs_filter / conf_now->l_current_max);
		L -= L * comp_fact;			// 电感补偿
		lambda -= lambda * comp_fact;	// 磁链补偿
	} break;

	case SAT_COMP_LAMBDA_AND_FACTOR: {
		// 组合补偿模式：同时使用磁链补偿和因子补偿
		if (conf_now->foc_observer_type >= FOC_OBSERVER_ORTEGA_LAMBDA_COMP) {
			L = L * (state->lambda_est / lambda);
		}
		const float comp_fact = conf_now->foc_sat_comp * (motor->m_motor_state.i_abs_filter / conf_now->l_current_max);
		L -= L * comp_fact;
	} break;

	default:
		break;
	}

	// 温度补偿
	// 电机温度升高时，定子电阻会增加，需要进行补偿
	if (conf_now->foc_temp_comp) {
		R = motor->m_res_temp_comp;	// 使用温度补偿后的电阻值
	}

	// 凸极性补偿
	// 对于内置式永磁同步电机(IPMSM)，d轴和q轴电感不相等
	// 需要根据当前dq电流计算等效电感
	float ld_lq_diff = conf_now->foc_motor_ld_lq_diff;	// Ld - Lq
	float id = motor->m_motor_state.id;					// d轴电流
	float iq = motor->m_motor_state.iq;					// q轴电流

	// 凸极性电感计算：L_eq = L - (Ld-Lq)/2 + (Ld-Lq)*Iq²/(Id²+Iq²)
	// 当电流较小时跳过计算，避免除零错误
	if (fabsf(id) > 0.1 || fabsf(iq) > 0.1) {
		L = L - ld_lq_diff / 2.0 + ld_lq_diff * SQ(iq) / (SQ(id) + SQ(iq));
	}

	// 预计算常用项，提高计算效率
	float L_ia = L * i_alpha;		// L * i_α
	float L_ib = L * i_beta;		// L * i_β
	const float R_ia = R * i_alpha;	// R * i_α
	const float R_ib = R * i_beta;	// R * i_β
	const float gamma_half = motor->m_gamma_now * 0.5;	// 观测器增益的一半

	// 根据配置的观测器类型执行相应算法
	switch (conf_now->foc_observer_type) {
	case FOC_OBSERVER_ORTEGA_ORIGINAL: {
		/*
		 * Ortega原始观测器算法
		 *
		 * 基于磁链幅值约束的非线性观测器：
		 * dx/dt = v - R*i + γ/2 * (x - L*i) * err
		 * 其中 err = λ² - |x - L*i|²
		 *
		 * 约束条件：|x - L*i| = λ (磁链幅值恒定)
		 * 转子位置：θ = atan2(x2 - L*i_β, x1 - L*i_α)
		 */
		float err = SQ(lambda) - (SQ(state->x1 - L_ia) + SQ(state->x2 - L_ib));

		// 强制误差项保持非正值有助于收敛性
		// 参考文献：
		// http://cas.ensmp.fr/Publications/Publications/Papers/ObserverPermanentMagnet.pdf
		// https://arxiv.org/pdf/1905.00833.pdf
		if (err > 0.0) {
			err = 0.0;
		}

		// 观测器状态方程：
		// dx1/dt = v_α - R*i_α + γ/2 * (x1 - L*i_α) * err
		// dx2/dt = v_β - R*i_β + γ/2 * (x2 - L*i_β) * err
		float x1_dot = v_alpha - R_ia + gamma_half * (state->x1 - L_ia) * err;
		float x2_dot = v_beta - R_ib + gamma_half * (state->x2 - L_ib) * err;

		// 数值积分更新状态
		state->x1 += x1_dot * dt;
		state->x2 += x2_dot * dt;
	} break;

	case FOC_OBSERVER_MXLEMMING:
	case FOC_OBSERVER_MXLEMMING_LAMBDA_COMP:
		/*
		 * MXLEMMING观测器算法
		 *
		 * 许可证说明：
		 * 此算法源自MESC FOC项目的原创工作，作者David Molony。
		 * 使用时需保留此说明并注明原作者。
		 *
		 * 算法原理：
		 * 基于电机电压方程的直接积分：
		 * ψ = ∫(v - R*i)dt
		 *
		 * 相比Ortega观测器，此算法更简单直接，但需要准确的电阻参数。
		 */
		// LICENCE NOTE:
		// This function deviates slightly from the BSD 3 clause licence.
		// The work here is entirely original to the MESC FOC project, and not based
		// on any appnotes, or borrowed from another project. This work is free to
		// use, as granted in BSD 3 clause, with the exception that this note must
		// be included in where this code is implemented/modified to use your
		// variable names, structures containing variables or other minor
		// rearrangements in place of the original names I have chosen, and credit
		// to David Molony as the original author must be noted.

		// 磁链积分：x = ∫(v - R*i)dt - L*(i - i_last)
		// 减去L*(i - i_last)项是为了补偿电感压降的瞬时变化
		state->x1 += (v_alpha - R_ia) * dt - L * (i_alpha - state->i_alpha_last);
		state->x2 += (v_beta - R_ib) * dt - L * (i_beta - state->i_beta_last);

		if (conf_now->foc_observer_type == FOC_OBSERVER_MXLEMMING_LAMBDA_COMP) {
			/*
			 * 带磁链补偿的MXLEMMING观测器
			 *
			 * 磁链观测器基于以下文献：
			 * https://cas.mines-paristech.fr/~praly/Telechargement/Conferences/2017_IFAC_Bernard-Praly.pdf
			 *
			 * 磁链估计方程：
			 * dλ_est/dt = 0.1 * γ/2 * λ_est * (λ_est² - |x|²)
			 */
			float err = SQ(state->lambda_est) - (SQ(state->x1) + SQ(state->x2));
			state->lambda_est += 0.1 * gamma_half * state->lambda_est * -err * dt;

			// 限制观测到的磁链幅值在合理范围内
			utils_truncate_number(&(state->lambda_est), lambda * 0.5, lambda * 1.5);

			// 限制观测器状态在磁链幅值范围内
			utils_truncate_number_abs(&(state->x1), state->lambda_est);
			utils_truncate_number_abs(&(state->x2), state->lambda_est);
		} else {
			// 标准MXLEMMING观测器：限制状态在额定磁链范围内
			utils_truncate_number_abs(&(state->x1), lambda);
			utils_truncate_number_abs(&(state->x2), lambda);
		}

		// 设置为0以便与Ortega观测器使用相同的atan2计算
		// 因为MXLEMMING直接观测磁链，不需要减去L*i项
		L_ia = 0.0;
		L_ib = 0.0;
		break;

	case FOC_OBSERVER_ORTEGA_LAMBDA_COMP: {
		/*
		 * 带磁链补偿的Ortega观测器
		 *
		 * 结合了Ortega观测器的稳定性和磁链自适应能力，
		 * 能够适应电机参数变化和磁路饱和。
		 *
		 * 参考文献：
		 * https://cas.mines-paristech.fr/~praly/Telechargement/Conferences/2017_IFAC_Bernard-Praly.pdf
		 */
		float err = SQ(state->lambda_est) - (SQ(state->x1 - L_ia) + SQ(state->x2 - L_ib));

		// 磁链观测器更新
		// 磁链估计方程：dλ_est/dt = 0.2 * γ/2 * λ_est * (λ_est² - |x - L*i|²)
		// 系数0.2比MXLEMMING的0.1更大，因为Ortega观测器对磁链变化更敏感
		state->lambda_est += 0.2 * gamma_half * state->lambda_est * -err * dt;

		// 限制观测到的磁链幅值在合理范围内(50%-150%额定值)
		utils_truncate_number(&(state->lambda_est), lambda * 0.5, lambda * 1.5);

		// 强制误差项保持非正值，确保观测器稳定性
		if (err > 0.0) {
			err = 0.0;
		}

		// Ortega观测器状态方程(与原始版本相同)
		float x1_dot = v_alpha - R_ia + gamma_half * (state->x1 - L_ia) * err;
		float x2_dot = v_beta - R_ib + gamma_half * (state->x2 - L_ib) * err;

		// 数值积分更新状态
		state->x1 += x1_dot * dt;
		state->x2 += x2_dot * dt;
	} break;

	default:
		break;
	}

	// 保存当前电流值供下次迭代使用
	state->i_alpha_last = i_alpha;
	state->i_beta_last = i_beta;

	// 防止NaN值传播，确保数值稳定性
	UTILS_NAN_ZERO(state->x1);
	UTILS_NAN_ZERO(state->x2);

	// 防止磁链幅值过小导致角度不稳定
	// 当观测器状态幅值小于额定磁链的50%时，适当放大
	float mag = NORM2_f(state->x1, state->x2);
	if (mag < (lambda * 0.5)) {
		state->x1 *= 1.1;	// 放大10%
		state->x2 *= 1.1;
	}

	// 计算转子电角度
	// θ = atan2(x2 - L*i_β, x1 - L*i_α)
	// 对于MXLEMMING观测器，L_ia和L_ib已设为0
	if (phase) {
		*phase = utils_fast_atan2(state->x2 - L_ib, state->x1 - L_ia);
	}
}

/**
 * ========================================================================
 * 锁相环(PLL)算法 - 相位和速度平滑跟踪
 * ========================================================================
 *
 * 功能：跟踪观测器输出的相位信号，提供平滑的相位和速度估计
 * 目的：滤除观测器输出中的噪声，提高控制系统的稳定性
 *
 * PLL原理：
 * 锁相环是一个反馈控制系统，能够使输出信号的相位锁定到输入信号的相位。
 * 在FOC控制中，PLL用于跟踪观测器估计的转子相位，提供平滑的相位输出。
 *
 * 数学模型：
 *
 * 1. 连续时间PLL方程：
 *    θ̇_pll = ω_pll + Kp × Δθ     (相位更新方程)
 *    ω̇_pll = Ki × Δθ             (频率更新方程)
 *
 *    其中：
 *    - θ_pll: PLL输出相位
 *    - ω_pll: PLL输出角频率
 *    - Δθ = θ_input - θ_pll: 相位误差
 *    - Kp: 比例增益（影响跟踪速度）
 *    - Ki: 积分增益（消除稳态误差）
 *
 * 2. 离散化实现：
 *    θ_pll(k+1) = θ_pll(k) + [ω_pll(k) + Kp × Δθ(k)] × dt
 *    ω_pll(k+1) = ω_pll(k) + Ki × Δθ(k) × dt
 *
 * 3. 传递函数：
 *    H(s) = (Kp×s + Ki) / (s² + Kp×s + Ki)
 *
 * 参数调节：
 * - Kp增大：跟踪速度快，但可能引起振荡
 * - Ki增大：消除稳态误差，但可能降低稳定性
 * - 典型值：Kp = 500-2000, Ki = 10000-50000
 *
 * 性能指标：
 * - 锁定时间：通常<10ms
 * - 相位误差：<1°
 * - 噪声抑制：>20dB
 *
 * 应用场景：
 * - 无传感器FOC控制中的相位平滑
 * - 编码器信号的噪声滤除
 * - 速度估计的平滑处理
 *
 * @param phase 输入相位信号 (rad)
 * @param dt 采样时间间隔 (s)
 * @param phase_var PLL相位变量指针 (rad)
 * @param speed_var PLL速度变量指针 (rad/s)
 * @param conf 电机配置参数（包含Kp和Ki）
 */
void foc_pll_run(float phase, float dt, float *phase_var,
					float *speed_var, mc_configuration *conf) {
	// 防止NaN值传播
	UTILS_NAN_ZERO(*phase_var);

	// 计算相位误差：Δθ = θ_input - θ_pll
	float delta_theta = phase - *phase_var;
	utils_norm_angle_rad(&delta_theta);	// 将角度差归一化到[-π, π]

	// 防止速度变量为NaN
	UTILS_NAN_ZERO(*speed_var);

	// PLL相位更新：θ_pll += (ω_pll + Kp * Δθ) * dt
	*phase_var += (*speed_var + conf->foc_pll_kp * delta_theta) * dt;
	utils_norm_angle_rad((float*)phase_var);	// 将相位归一化到[-π, π]

	// PLL速度更新：ω_pll += Ki * Δθ * dt
	*speed_var += conf->foc_pll_ki * delta_theta * dt;
}

/**
 * ========================================================================
 * 空间矢量调制(SVPWM)算法 - FOC控制的核心输出算法
 * ========================================================================
 *
 * 功能：将αβ坐标系的电压矢量转换为三相PWM占空比
 * 目的：实现高效率、低谐波的三相PWM输出
 *
 * SVPWM相比传统正弦PWM的优势：
 * 1. 直流电压利用率提高15.47% (从86.6%提升到100%)
 * 2. 谐波含量更低，电机运行更平滑
 * 3. 开关损耗更小，效率更高
 * 4. 更好的动态响应特性
 *
 * 数学原理：
 *
 * 1. 基本矢量定义：
 *    三相逆变器有8个开关状态，对应8个基本矢量：
 *    V0(000): (0, 0)           - 零矢量
 *    V1(100): (2/3, 0)         - 0°
 *    V2(110): (1/3, √3/3)      - 60°
 *    V3(010): (-1/3, √3/3)     - 120°
 *    V4(011): (-2/3, 0)        - 180°
 *    V5(001): (-1/3, -√3/3)    - 240°
 *    V6(101): (1/3, -√3/3)     - 300°
 *    V7(111): (0, 0)           - 零矢量
 *
 * 2. 扇区划分：
 *    将αβ平面划分为6个扇区，每个扇区60°：
 *    扇区1: 0°~60°     使用V1, V2
 *    扇区2: 60°~120°   使用V2, V3
 *    扇区3: 120°~180°  使用V3, V4
 *    扇区4: 180°~240°  使用V4, V5
 *    扇区5: 240°~300°  使用V5, V6
 *    扇区6: 300°~360°  使用V6, V1
 *
 * 3. 矢量合成：
 *    目标矢量 = (t1/T)×V_i + (t2/T)×V_{i+1} + (t0/T)×V_0
 *    其中：t1, t2为相邻基本矢量作用时间
 *          t0为零矢量作用时间
 *          T为PWM周期，满足 t1 + t2 + t0 = T
 *
 * 4. 对称分布：
 *    为减少开关损耗，采用对称分布：
 *    V0 → V_i → V_{i+1} → V7 → V_{i+1} → V_i → V0
 *
 * 5. 电压约束：
 *    为避免过调制：|V_αβ| ≤ √3/2 ≈ 0.866
 *
 * 性能指标：
 * - 最大调制度：100% (vs 正弦PWM的86.6%)
 * - THD改善：约30%
 * - 开关频率：与载波频率相同
 * - 计算复杂度：O(1)
 *
 * 参考：https://github.com/vedderb/bldc/pull/372#issuecomment-962499623
 *
 * @param alpha α轴电压 (标幺值，范围[-0.866, 0.866])
 * @param beta  β轴电压 (标幺值，范围[-0.866, 0.866])
 * @param PWMFullDutyCycle PWM计数器峰值 (通常为系统时钟/PWM频率)
 * @param tAout A相PWM占空比输出 (0=全关，PWMFullDutyCycle=全开)
 * @param tBout B相PWM占空比输出
 * @param tCout C相PWM占空比输出
 * @param svm_sector SVM扇区号输出(1-6，用于调试和优化)
 */
void foc_svm(float alpha, float beta, uint32_t PWMFullDutyCycle,
				uint32_t* tAout, uint32_t* tBout, uint32_t* tCout, uint32_t *svm_sector) {
	uint32_t sector;

	/*
	 * 扇区判断算法
	 *
	 * 将αβ平面划分为6个扇区，每个扇区60°：
	 * 扇区1: 0°~60°    (α>0, β>0, β/α < √3)
	 * 扇区2: 60°~120°  (α<0, β>0, β/|α| < √3) 或 (α>0, β>0, β/α > √3)
	 * 扇区3: 120°~180° (α<0, β>0, β/|α| > √3)
	 * 扇区4: 180°~240° (α<0, β<0, |β|/|α| < √3)
	 * 扇区5: 240°~300° (α<0, β<0, |β|/|α| > √3) 或 (α>0, β<0, |β|/α < √3)
	 * 扇区6: 300°~360° (α>0, β<0, |β|/α > √3)
	 *
	 * 判断条件使用 1/√3 ≈ 0.577 作为斜率阈值
	 */
	if (beta >= 0.0f) {
		if (alpha >= 0.0f) {
			// 第一象限 (α>0, β>0)
			if (ONE_BY_SQRT3 * beta > alpha) {
				sector = 2;	// β/α > √3，扇区2
			} else {
				sector = 1;	// β/α ≤ √3，扇区1
			}
		} else {
			// 第二象限 (α<0, β>0)
			if (-ONE_BY_SQRT3 * beta > alpha) {
				sector = 3;	// β/|α| > √3，扇区3
			} else {
				sector = 2;	// β/|α| ≤ √3，扇区2
			}
		}
	} else {
		if (alpha >= 0.0f) {
			// 第四象限 (α>0, β<0)
			if (-ONE_BY_SQRT3 * beta > alpha) {
				sector = 5;	// |β|/α > √3，扇区5
			} else {
				sector = 6;	// |β|/α ≤ √3，扇区6
			}
		} else {
			// 第三象限 (α<0, β<0)
			if (ONE_BY_SQRT3 * beta > alpha) {
				sector = 4;	// |β|/|α| ≤ √3，扇区4
			} else {
				sector = 5;	// |β|/|α| > √3，扇区5
			}
		}
	}

	// PWM占空比计算
	uint32_t tA, tB, tC;

	/*
	 * 各扇区的矢量合成计算
	 *
	 * 每个扇区使用相邻的两个基本矢量合成目标矢量：
	 * V_target = (t1/T) * V_i + (t2/T) * V_{i+1} + (t0/T) * V_0
	 * 其中 t1, t2 为基本矢量作用时间，t0 为零矢量作用时间
	 * T = t1 + t2 + t0 为PWM周期
	 *
	 * 对称分布：为了减少开关损耗，零矢量时间平均分配到周期两端
	 */
	switch (sector) {

	// 扇区1：使用V1(1,0)和V2(1/2,√3/2)
	case 1: {
		/*
		 * 扇区1矢量方程：
		 * [α] = t1/T * [1] + t2/T * [1/2]
		 * [β]         [0]         [√3/2]
		 *
		 * 解得：t1 = T * (α - β/√3)
		 *      t2 = T * (2β/√3)
		 */
		// 基本矢量作用时间计算
		uint32_t t1 = (alpha - ONE_BY_SQRT3 * beta) * PWMFullDutyCycle;
		uint32_t t2 = (TWO_BY_SQRT3 * beta) * PWMFullDutyCycle;

		// PWM占空比计算 (对称分布)
		// 开关序列：V0 → V1 → V2 → V7 → V2 → V1 → V0
		tA = (PWMFullDutyCycle + t1 + t2) / 2;	// A相：高电平时间最长
		tB = tA - t1;							// B相：中等高电平时间
		tC = tB - t2;							// C相：高电平时间最短

		break;
	}

	// 扇区2：使用V2(1/2,√3/2)和V3(-1/2,√3/2)
	case 2: {
		/*
		 * 扇区2矢量方程：
		 * [α] = t2/T * [1/2] + t3/T * [-1/2]
		 * [β]         [√3/2]        [√3/2]
		 *
		 * 解得：t2 = T * (α + β/√3)
		 *      t3 = T * (-α + β/√3)
		 */
		// 基本矢量作用时间计算
		uint32_t t2 = (alpha + ONE_BY_SQRT3 * beta) * PWMFullDutyCycle;
		uint32_t t3 = (-alpha + ONE_BY_SQRT3 * beta) * PWMFullDutyCycle;

		// PWM占空比计算
		tB = (PWMFullDutyCycle + t2 + t3) / 2;	// B相：高电平时间最长
		tA = tB - t3;							// A相：中等高电平时间
		tC = tA - t2;							// C相：高电平时间最短

		break;
	}

	// 扇区3：使用V3(-1/2,√3/2)和V4(-1,0)
	case 3: {
		/*
		 * 扇区3矢量方程：
		 * [α] = t3/T * [-1/2] + t4/T * [-1]
		 * [β]         [√3/2]         [0]
		 *
		 * 解得：t3 = T * (2β/√3)
		 *      t4 = T * (-α - β/√3)
		 */
		// 基本矢量作用时间计算
		uint32_t t3 = (TWO_BY_SQRT3 * beta) * PWMFullDutyCycle;
		uint32_t t4 = (-alpha - ONE_BY_SQRT3 * beta) * PWMFullDutyCycle;

		// PWM占空比计算
		tB = (PWMFullDutyCycle + t3 + t4) / 2;	// B相：高电平时间最长
		tC = tB - t3;							// C相：中等高电平时间
		tA = tC - t4;							// A相：高电平时间最短

		break;
	}

	// 扇区4：使用V4(-1,0)和V5(-1/2,-√3/2)
	case 4: {
		/*
		 * 扇区4矢量方程：
		 * [α] = t4/T * [-1] + t5/T * [-1/2]
		 * [β]         [0]          [-√3/2]
		 *
		 * 解得：t4 = T * (-α + β/√3)
		 *      t5 = T * (-2β/√3)
		 */
		// 基本矢量作用时间计算
		uint32_t t4 = (-alpha + ONE_BY_SQRT3 * beta) * PWMFullDutyCycle;
		uint32_t t5 = (-TWO_BY_SQRT3 * beta) * PWMFullDutyCycle;

		// PWM占空比计算
		tC = (PWMFullDutyCycle + t4 + t5) / 2;	// C相：高电平时间最长
		tB = tC - t5;							// B相：中等高电平时间
		tA = tB - t4;							// A相：高电平时间最短

		break;
	}

	// 扇区5：使用V5(-1/2,-√3/2)和V6(1/2,-√3/2)
	case 5: {
		/*
		 * 扇区5矢量方程：
		 * [α] = t5/T * [-1/2] + t6/T * [1/2]
		 * [β]         [-√3/2]        [-√3/2]
		 *
		 * 解得：t5 = T * (-α - β/√3)
		 *      t6 = T * (α - β/√3)
		 */
		// 基本矢量作用时间计算
		uint32_t t5 = (-alpha - ONE_BY_SQRT3 * beta) * PWMFullDutyCycle;
		uint32_t t6 = (alpha - ONE_BY_SQRT3 * beta) * PWMFullDutyCycle;

		// PWM占空比计算
		tC = (PWMFullDutyCycle + t5 + t6) / 2;	// C相：高电平时间最长
		tA = tC - t5;							// A相：中等高电平时间
		tB = tA - t6;							// B相：高电平时间最短

		break;
	}

	// 扇区6：使用V6(1/2,-√3/2)和V1(1,0)
	case 6: {
		/*
		 * 扇区6矢量方程：
		 * [α] = t6/T * [1/2] + t1/T * [1]
		 * [β]         [-√3/2]        [0]
		 *
		 * 解得：t6 = T * (-2β/√3)
		 *      t1 = T * (α + β/√3)
		 */
		// 基本矢量作用时间计算
		uint32_t t6 = (-TWO_BY_SQRT3 * beta) * PWMFullDutyCycle;
		uint32_t t1 = (alpha + ONE_BY_SQRT3 * beta) * PWMFullDutyCycle;

		// PWM占空比计算
		tA = (PWMFullDutyCycle + t6 + t1) / 2;	// A相：高电平时间最长
		tC = tA - t1;							// C相：中等高电平时间
		tB = tC - t6;							// B相：高电平时间最短

		break;
	}
	}

	// 输出计算结果
	*tAout = tA;			// A相PWM占空比
	*tBout = tB;			// B相PWM占空比
	*tCout = tC;			// C相PWM占空比
	*svm_sector = sector;	// 当前扇区号(1-6)
}

/**
 * 位置PID控制函数
 *
 * 实现位置闭环控制，将位置误差转换为转矩电流命令。
 * 采用增强型PID算法，包含以下高级功能：
 * 1. 增益调度 - 根据位置误差大小调整PID参数
 * 2. 积分抗饱和 - 防止积分项过大
 * 3. 微分滤波 - 减少微分项噪声
 * 4. 过程微分 - 基于反馈值而非误差的微分项
 *
 * PID控制方程：
 * u(t) = Kp·e(t) + Ki·∫e(t)dt + Kd·de(t)/dt + Kd_proc·d(PV)/dt
 * 其中：e(t) = SP - PV (设定值 - 过程值)
 *
 * @param index_found 编码器索引是否找到(影响控制精度)
 * @param dt          控制周期 (s)
 * @param motor       电机状态结构体指针
 */
void foc_run_pid_control_pos(bool index_found, float dt, motor_all_state_t *motor) {
	mc_configuration *conf_now = motor->m_conf;

	// 获取当前位置和设定位置
	float angle_now = motor->m_pos_pid_now;	// 当前位置反馈值 (rad)
	float angle_set = motor->m_pos_pid_set;	// 位置设定值 (rad)

	// PID项变量声明
	float p_term;		// 比例项
	float d_term;		// 微分项(基于误差)
	float d_term_proc;	// 过程微分项(基于反馈值)

	// 检查是否为位置控制模式
	if (motor->m_control_mode != CONTROL_MODE_POS) {
		// 非位置控制模式，重置所有PID状态变量
		motor->m_pos_i_term = 0;				// 清零积分项
		motor->m_pos_prev_error = 0;			// 清零上次误差
		motor->m_pos_prev_proc = angle_now;		// 更新上次过程值
		motor->m_pos_d_filter = 0.0;			// 清零微分滤波器
		motor->m_pos_d_filter_proc = 0.0;		// 清零过程微分滤波器
		return;
	}

	// 计算位置误差
	// 使用角度差函数处理角度跨越±π的情况
	float error = utils_angle_difference(angle_set, angle_now);
	float error_sign = 1.0;	// 误差符号修正因子

	// 编码器方向修正
	if (conf_now->m_sensor_port_mode != SENSOR_PORT_MODE_HALL) {
		if (conf_now->foc_encoder_inverted) {
			error_sign = -1.0;	// 编码器反向时修正误差符号
		}
	}

	error *= error_sign;	// 应用符号修正

	// 获取PID参数
	float kp = conf_now->p_pid_kp;			// 比例增益
	float ki = conf_now->p_pid_ki;			// 积分增益
	float kd = conf_now->p_pid_kd;			// 微分增益
	float kd_proc = conf_now->p_pid_kd_proc;// 过程微分增益

	/*
	 * 增益调度算法
	 *
	 * 当位置误差较小时，降低PID增益以提高稳定性和减少振荡。
	 * 这种方法在精密定位应用中特别有用。
	 *
	 * 调度策略：
	 * - 当 |error| < min_error 时，按比例缩放所有增益
	 * - 缩放因子 = |error| / min_error
	 * - min_error = gain_dec_angle / ang_div
	 */
	if (conf_now->p_pid_gain_dec_angle > 0.1) {
		// 计算最小误差阈值
		float min_error = conf_now->p_pid_gain_dec_angle / conf_now->p_pid_ang_div;
		float error_abs = fabs(error);

		// 当误差小于阈值时，按比例缩放增益
		if (error_abs < min_error) {
			float scale = error_abs / min_error;	// 缩放因子 [0, 1]
			kp *= scale;		// 缩放比例增益
			ki *= scale;		// 缩放积分增益
			kd *= scale;		// 缩放微分增益
			kd_proc *= scale;	// 缩放过程微分增益
		}
	}

	// 计算比例项：P = Kp * e(t)
	p_term = error * kp;

	// 计算积分项：I = Ki * ∫e(t)dt
	// 使用矩形积分法：I(k+1) = I(k) + Ki * e(k) * dt
	motor->m_pos_i_term += error * (ki * dt);

	/*
	 * 微分项计算 (基于误差)
	 *
	 * 采用变采样时间的微分算法：
	 * D = Kd * de(t)/dt = Kd * [e(k) - e(k-1)] / Δt_accumulated
	 *
	 * 特殊处理：当误差不变时(通常发生在低速时位置分辨率不足)，
	 * 累积时间间隔直到误差发生变化，然后计算平均微分。
	 * 这种方法可以避免在位置更新缓慢时产生错误的微分信号。
	 */
	motor->m_pos_dt_int += dt;	// 累积时间间隔
	if (error == motor->m_pos_prev_error) {
		// 误差未变化，微分项为零
		d_term = 0.0;
	} else {
		// 误差发生变化，计算微分项
		d_term = (error - motor->m_pos_prev_error) * (kd / motor->m_pos_dt_int);
		motor->m_pos_dt_int = 0.0;	// 重置时间累积器
	}

	// 微分项低通滤波
	// 使用快速低通滤波器减少微分项的高频噪声
	UTILS_LP_FAST(motor->m_pos_d_filter, d_term, conf_now->p_pid_kd_filter);
	d_term = motor->m_pos_d_filter;

	/*
	 * 过程微分项计算 (基于反馈值)
	 *
	 * 过程微分项基于反馈值而非误差：
	 * D_proc = -Kd_proc * d(PV)/dt = -Kd_proc * [PV(k) - PV(k-1)] / Δt
	 *
	 * 优势：
	 * 1. 避免设定值突变时的微分冲击
	 * 2. 提供更平滑的控制输出
	 * 3. 改善系统的阻尼特性
	 */
	motor->m_pos_dt_int_proc += dt;	// 累积过程时间间隔
	if (angle_now == motor->m_pos_prev_proc) {
		// 反馈值未变化，过程微分项为零
		d_term_proc = 0.0;
	} else {
		// 反馈值发生变化，计算过程微分项
		// 注意负号：过程微分项与反馈值变化方向相反
		d_term_proc = -utils_angle_difference(angle_now, motor->m_pos_prev_proc) * error_sign * (kd_proc / motor->m_pos_dt_int_proc);
		motor->m_pos_dt_int_proc = 0.0;	// 重置过程时间累积器
	}

	// 过程微分项低通滤波
	UTILS_LP_FAST(motor->m_pos_d_filter_proc, d_term_proc, conf_now->p_pid_kd_filter);
	d_term_proc = motor->m_pos_d_filter_proc;

	/*
	 * 积分抗饱和保护
	 *
	 * 防止积分项过大导致系统饱和和超调。
	 * 策略：根据比例项的大小动态限制积分项的最大值。
	 *
	 * 算法：
	 * 1. 限制比例项在[-1, 1]范围内
	 * 2. 积分项限制 = 1.0 - |比例项|
	 * 3. 确保 |比例项| + |积分项| ≤ 1.0
	 */
	float p_tmp = p_term;
	utils_truncate_number_abs(&p_tmp, 1.0);	// 限制比例项幅值
	utils_truncate_number_abs((float*)&motor->m_pos_i_term, 1.0 - fabsf(p_tmp));	// 动态限制积分项

	// 保存当前值供下次迭代使用
	motor->m_pos_prev_error = error;		// 保存当前误差
	motor->m_pos_prev_proc = angle_now;		// 保存当前反馈值

	// 计算PID控制器总输出
	// u(t) = P + I + D + D_proc
	float output = p_term + motor->m_pos_i_term + d_term + d_term_proc;
	utils_truncate_number(&output, -1.0, 1.0);	// 限制输出在[-1, 1]范围内

	/*
	 * 转换为转矩电流命令
	 *
	 * 根据传感器类型和编码器索引状态确定输出策略：
	 * 1. 非霍尔传感器 + 索引未找到：以40%功率旋转寻找索引
	 * 2. 其他情况：正常PID输出
	 */
	if (conf_now->m_sensor_port_mode != SENSOR_PORT_MODE_HALL) {
		if (index_found) {
			// 编码器索引已找到，使用正常PID输出
			motor->m_iq_set = output * conf_now->l_current_max * conf_now->l_current_max_scale;
		} else {
			// 编码器索引未找到，以40%功率旋转电机寻找索引位置
			motor->m_iq_set = 0.4 * conf_now->l_current_max * conf_now->l_current_max_scale;
		}
	} else {
		// 霍尔传感器模式，直接使用PID输出
		motor->m_iq_set = output * conf_now->l_current_max * conf_now->l_current_max_scale;
	}
}

/**
 * 速度PID控制函数
 *
 * 实现速度闭环控制，将速度误差转换为转矩电流命令。
 * 包含以下功能：
 * 1. 速度斜坡控制 - 平滑的速度变化
 * 2. 最小转速保护 - 避免低速时的不稳定
 * 3. 积分抗饱和 - 防止积分项过大
 * 4. 微分滤波 - 减少速度反馈噪声
 *
 * PID控制方程：
 * u(t) = Kp·e(t) + Ki·∫e(t)dt + Kd·de(t)/dt
 * 其中：e(t) = ω_set - ω_actual (设定转速 - 实际转速)
 *
 * @param dt    控制周期 (s)
 * @param motor 电机状态结构体指针
 */
void foc_run_pid_control_speed(float dt, motor_all_state_t *motor) {
	mc_configuration *conf_now = motor->m_conf;
	float p_term;	// 比例项
	float d_term;	// 微分项

	// 检查是否为速度控制模式
	if (motor->m_control_mode != CONTROL_MODE_SPEED) {
		// 非速度控制模式，重置所有PID状态变量
		motor->m_speed_i_term = 0.0;		// 清零积分项
		motor->m_speed_prev_error = 0.0;	// 清零上次误差
		motor->m_speed_d_filter = 0.0;		// 清零微分滤波器
		return;
	}

	/*
	 * 速度斜坡控制
	 *
	 * 为了避免速度设定值的突变导致系统冲击，使用斜坡函数
	 * 平滑地改变速度设定值。
	 *
	 * 斜坡速率：s_pid_ramp_erpms_s (ERPM/s)
	 */
	if (conf_now->s_pid_ramp_erpms_s > 0.0) {
		utils_step_towards((float*)&motor->m_speed_pid_set_rpm,
						   motor->m_speed_command_rpm,
						   conf_now->s_pid_ramp_erpms_s * dt);
	}

	// 获取当前转速并计算误差
	const float rpm = RADPS2RPM_f(motor->m_motor_state.speed_rad_s);	// 当前转速 (RPM)
	float error = motor->m_speed_pid_set_rpm - rpm;						// 速度误差 (RPM)

	/*
	 * 最小转速保护
	 *
	 * 当设定转速过低时，停止控制并释放电机。
	 * 这可以避免在极低速时的控制不稳定和能耗。
	 */
	if (fabsf(motor->m_speed_pid_set_rpm) < conf_now->s_pid_min_erpm) {
		motor->m_speed_i_term = 0.0;		// 清零积分项
		motor->m_speed_prev_error = error;	// 保存当前误差
		motor->m_iq_set = 0.0;				// 停止输出转矩
		return;
	}

	/*
	 * PID参数计算
	 *
	 * 注意：所有PID增益都乘以缩放因子 (1.0/20.0)
	 * 这是为了将内部PID计算范围调整到合适的数值范围。
	 */
	// 比例项：P = Kp * e(t) * scale
	p_term = error * conf_now->s_pid_kp * (1.0 / 20.0);

	// 微分项：D = Kd * de(t)/dt * scale
	d_term = (error - motor->m_speed_prev_error) * (conf_now->s_pid_kd / dt) * (1.0 / 20.0);

	// 微分项低通滤波
	// 减少速度反馈中的高频噪声对微分项的影响
	UTILS_LP_FAST(motor->m_speed_d_filter, d_term, conf_now->s_pid_kd_filter);
	d_term = motor->m_speed_d_filter;

	// 保存当前误差供下次迭代使用
	motor->m_speed_prev_error = error;

	// 计算PID控制器总输出
	// u(t) = P + I + D
	float output = p_term + motor->m_speed_i_term + d_term;
	utils_truncate_number_abs(&output, 1.0);	// 限制输出幅值

	/*
	 * 积分项更新和抗饱和保护
	 *
	 * 积分项：I = Ki * ∫e(t)dt * scale
	 * 使用矩形积分法：I(k+1) = I(k) + Ki * e(k) * dt
	 */
	motor->m_speed_i_term += error * conf_now->s_pid_ki * dt * (1.0 / 20.0);
	utils_truncate_number_abs(&motor->m_speed_i_term, 1.0);	// 积分抗饱和

	// 当积分增益极小时，禁用积分项
	if (conf_now->s_pid_ki < 1e-9) {
		motor->m_speed_i_term = 0.0;
	}

	/*
	 * 可选的制动禁用功能
	 *
	 * 在某些应用中，可能不希望电机进行再生制动。
	 * 当检测到制动条件时(转速与输出方向相反)，将输出设为零。
	 *
	 * 制动检测条件：
	 * - 正转且输出为负 (rpm > 20 && output < 0)
	 * - 反转且输出为正 (rpm < -20 && output > 0)
	 */
	if (!conf_now->s_pid_allow_braking) {
		if (rpm > 20.0 && output < 0.0) {
			output = 0.0;	// 禁用正转时的制动
		}

		if (rpm < -20.0 && output > 0.0) {
			output = 0.0;	// 禁用反转时的制动
		}
	}

	// 转换为转矩电流命令
	// 使用低速电流限制 (lo_current_max) 而非高速电流限制
	motor->m_iq_set = output * conf_now->lo_current_max * conf_now->l_current_max_scale;
}

/**
 * 编码器角度校正函数
 *
 * 在观测器角度和编码器角度之间进行智能切换，根据转速自动选择
 * 最可靠的角度源。这种混合方法结合了两种方法的优势：
 * - 低速时使用编码器：精度高，不受电机参数影响
 * - 高速时使用观测器：响应快，不受机械振动影响
 *
 * 切换策略：
 * - 使用滞回控制避免在切换点附近的振荡
 * - 切换阈值：sl_erpm ± 5%
 *
 * @param obs_angle 观测器估计的角度 (rad)
 * @param enc_angle 编码器测量的角度 (rad)
 * @param speed     当前转速 (rad/s)
 * @param sl_erpm   切换转速阈值 (ERPM)
 * @param motor     电机状态结构体指针
 * @return          校正后的角度 (rad)
 */
float foc_correct_encoder(float obs_angle, float enc_angle, float speed,
							 float sl_erpm, motor_all_state_t *motor) {
	// 计算当前转速的绝对值 (RPM)
	float rpm_abs = fabsf(RADPS2RPM_f(speed));

	/*
	 * 滞回控制逻辑
	 *
	 * 使用5%的滞回带避免在切换点附近的频繁切换：
	 * - 当前使用编码器：rpm > (sl_erpm + 5%) 时切换到观测器
	 * - 当前使用观测器：rpm < (sl_erpm - 5%) 时切换到编码器
	 */
	float hyst = sl_erpm * 0.05;	// 滞回带：切换阈值的5%
	if (motor->m_using_encoder) {
		// 当前使用编码器，检查是否需要切换到观测器
		if (rpm_abs > (sl_erpm + hyst)) {
			motor->m_using_encoder = false;	// 切换到观测器模式
		}
	} else {
		// 当前使用观测器，检查是否需要切换到编码器
		if (rpm_abs < (sl_erpm - hyst)) {
			motor->m_using_encoder = true;	// 切换到编码器模式
		}
	}

	// 根据当前模式返回相应的角度值
	return motor->m_using_encoder ? enc_angle : obs_angle;
}

/**
 * 霍尔传感器角度校正函数
 *
 * 处理霍尔传感器信号，提供角度估计和插值。霍尔传感器通常提供
 * 60°或120°的离散位置信息，需要通过插值获得连续的角度估计。
 *
 * 工作原理：
 * 1. 根据转速在霍尔传感器和观测器之间切换
 * 2. 霍尔传感器角度插值和速度限制
 * 3. 处理霍尔传感器的跳变和无效值
 *
 * @param angle    输入角度 (通常来自观测器) (rad)
 * @param dt       采样时间间隔 (s)
 * @param motor    电机状态结构体指针
 * @param hall_val 霍尔传感器值 (0-7，其中0和7通常无效)
 * @return         校正后的角度 (rad)
 */
float foc_correct_hall(float angle, float dt, motor_all_state_t *motor, int hall_val) {
	mc_configuration *conf_now = motor->m_conf;
	motor->m_hall_dt_diff_now += dt;	// 累积时间差

	/*
	 * 霍尔传感器速度计算
	 *
	 * 基于霍尔传感器跳变时间计算转速：
	 * ω = (π/3) / Δt = 60° / Δt
	 * 其中 Δt 为相邻霍尔跳变的时间间隔
	 */
	float rad_per_sec = (M_PI / 3.0) / motor->m_hall_dt_diff_last;
	float rpm_abs_fast = fabsf(RADPS2RPM_f(motor->m_speed_est_fast));	// 快速速度估计
	float rpm_abs_hall = fabsf(RADPS2RPM_f(rad_per_sec));				// 霍尔速度估计

	/*
	 * 霍尔传感器与观测器切换逻辑
	 *
	 * 使用20%的滞回带避免频繁切换：
	 * - 当前使用霍尔：min(rpm_fast, rpm_hall) > (sl_erpm + 20%) 时切换到观测器
	 * - 当前使用观测器：rpm_fast < (sl_erpm - 20%) 时切换到霍尔
	 */
	float hyst = conf_now->foc_sl_erpm * 0.2;	// 20%滞回带
	if (motor->m_using_hall) {
		// 当前使用霍尔传感器，检查是否需要切换到观测器
		if (fminf(rpm_abs_fast, rpm_abs_hall) > (conf_now->foc_sl_erpm + hyst)) {
			motor->m_using_hall = false;	// 切换到观测器模式
		}
	} else {
		// 当前使用观测器，检查是否需要切换到霍尔传感器
		if (rpm_abs_fast < (conf_now->foc_sl_erpm - hyst)) {
			motor->m_using_hall = true;		// 切换到霍尔传感器模式
		}
	}

	// 从霍尔查找表获取角度值
	int ang_hall_int = conf_now->foc_hall_table[hall_val];

	/*
	 * 霍尔传感器角度处理
	 *
	 * 霍尔查找表将3位霍尔值(1-6)映射到角度值(0-199)
	 * 角度值201表示无效的霍尔状态(通常是0或7)
	 */
	if (ang_hall_int < 201) {
		// 霍尔传感器值有效，进行角度转换
		// 将查找表值(0-199)缩放到弧度(0-2π)
		float ang_hall_now = ((float)ang_hall_int / 200.0) * 2 * M_PI;

		/*
		 * 霍尔传感器角度跳变处理
		 */
		if (motor->m_ang_hall_int_prev < 0) {
			// 上次角度无效(初始化状态)，直接使用当前角度
			motor->m_ang_hall_int_prev = ang_hall_int;
			motor->m_ang_hall = ang_hall_now;
		} else if (ang_hall_int != motor->m_ang_hall_int_prev) {
			/*
			 * 霍尔传感器发生跳变，需要处理角度差值
			 *
			 * 由于角度是循环的(0-199对应0-2π)，需要处理跨越边界的情况：
			 * - 如果差值 > 100，说明是反向跨越，实际差值应减去200
			 * - 如果差值 < -100，说明是正向跨越，实际差值应加上200
			 */
			int diff = ang_hall_int - motor->m_ang_hall_int_prev;
			if (diff > 100) {
				diff -= 200;	// 处理反向跨越边界
			} else if (diff < -100) {
				diff += 200;	// 处理正向跨越边界
			}

			/*
			 * 方向变化检测和时间差更新
			 *
			 * 只有当转动方向没有改变时，才更新时间差。
			 * 如果方向改变了，使用上次的时间差但改变符号。
			 */
			if (SIGN(diff) == SIGN(motor->m_hall_dt_diff_last)) {
				// 方向未改变，更新时间差
				if (diff > 0) {
					motor->m_hall_dt_diff_last = motor->m_hall_dt_diff_now;
				} else {
					motor->m_hall_dt_diff_last = -motor->m_hall_dt_diff_now;
				}
			} else {
				// 方向改变，使用上次时间差的相反值
				motor->m_hall_dt_diff_last = -motor->m_hall_dt_diff_last;
			}

			motor->m_hall_dt_diff_now = 0.0;	// 重置当前时间差

			/*
			 * 跳变时的角度估计
			 *
			 * 当霍尔传感器跳变时，实际转子位置在新旧角度的中间。
			 * 这种估计可以减少霍尔传感器的量化误差。
			 */
			int ang_avg = motor->m_ang_hall_int_prev + diff / 2;
			ang_avg %= 200;	// 确保角度在有效范围内

			// 转换为弧度
			motor->m_ang_hall = ((float)ang_avg / 200.0) * 2 * M_PI;
		}

		// 保存当前霍尔角度值供下次使用
		motor->m_ang_hall_int_prev = ang_hall_int;

		/*
		 * 霍尔传感器角度插值策略
		 *
		 * 根据转速决定是否进行角度插值：
		 * - 极低速时：直接使用霍尔传感器角度，避免插值误差
		 * - 正常速度：使用线性插值提供平滑的角度变化
		 */
		if (RADPS2RPM_f((M_PI / 3.0) /
				fmaxf(fabsf(motor->m_hall_dt_diff_now),
						fabsf(motor->m_hall_dt_diff_last))) < conf_now->foc_hall_interp_erpm) {
			/*
			 * 极低速模式：不进行插值
			 *
			 * 在极低速时，直接使用最近的霍尔传感器角度。
			 * 原因：如果在两个霍尔步骤之间发生方向变化，
			 * 插值可能导致60度的角度偏差。
			 */
			motor->m_ang_hall = ang_hall_now;
		} else {
			/*
			 * 正常速度模式：角度插值
			 *
			 * 使用线性插值在霍尔传感器跳变之间提供平滑的角度变化：
			 * θ(t) = θ(t-1) + ω * Δt
			 */
			float diff = utils_angle_difference_rad(motor->m_ang_hall, ang_hall_now);
			if (fabsf(diff) < ((2.0 * M_PI) / 12.0)) {
				// 角度差小于30度，进行线性插值
				motor->m_ang_hall += rad_per_sec * dt;
			} else {
				/*
				 * 插值偏差过大的修正
				 *
				 * 当插值角度与霍尔传感器角度差异过大时(>30度)，
				 * 逐步修正插值角度，避免突然的角度跳变。
				 */
				motor->m_ang_hall -= diff / 100.0;
			}
		}

		/*
		 * 霍尔传感器角度变化率限制
		 *
		 * 限制霍尔传感器角度的变化速率，防止角度估计快速变化时
		 * 在电流控制器中产生电流尖峰。
		 *
		 * 最大角度步长 = (转速/60) * 2π * dt * 1.5
		 * 其中1.5是安全系数，确保在正常运行时不会限制角度变化
		 */
		float angle_step = (fmaxf(rpm_abs_hall, conf_now->foc_hall_interp_erpm) / 60.0) * 2.0 * M_PI * dt * 1.5;
		float angle_diff = utils_angle_difference_rad(motor->m_ang_hall, motor->m_ang_hall_rate_limited);
		if (fabsf(angle_diff) < angle_step) {
			// 角度变化在允许范围内，直接更新
			motor->m_ang_hall_rate_limited = motor->m_ang_hall;
		} else {
			// 角度变化过大，按最大步长限制
			motor->m_ang_hall_rate_limited += angle_step * SIGN(angle_diff);
		}

		// 将角度归一化到[-π, π]范围
		utils_norm_angle_rad((float*)&motor->m_ang_hall_rate_limited);
		utils_norm_angle_rad((float*)&motor->m_ang_hall);

		// 如果当前使用霍尔传感器，更新输出角度
		if (motor->m_using_hall) {
			angle = motor->m_ang_hall_rate_limited;
		}
	} else {
		/*
		 * 无效的霍尔传感器读数处理
		 *
		 * 当霍尔传感器值无效时(通常是0或7)：
		 * 1. 不更新角度估计
		 * 2. 重置霍尔传感器状态
		 * 3. 允许开环运行以保持电机运转
		 */
		motor->m_ang_hall_int_prev = -1;	// 标记霍尔角度无效

		/*
		 * 开环运行模式
		 *
		 * 当霍尔传感器失效时，允许开环运行以模拟正常的无传感器操作。
		 * 这样即使霍尔传感器电缆断开，电机仍能继续工作
		 * (前提是传感器间距为120度)。
		 */
		if (motor->m_phase_observer_override && motor->m_state == MC_STATE_RUNNING) {
			angle = motor->m_phase_now_observer_override;
		}
	}

	return angle;
}

/**
 * 弱磁控制函数
 *
 * 实现弱磁控制算法，通过注入负d轴电流削弱永磁体磁场，
 * 降低反电动势，使电机能在更高转速下运行。
 *
 * 弱磁原理：
 * 1. 永磁同步电机的反电动势 E = ω * λ_pm
 * 2. 当转速增加时，反电动势增大，限制了可用电压
 * 3. 通过负d轴电流产生去磁磁场：λ_total = λ_pm - Ld * Id
 * 4. 有效降低总磁链，减少反电动势，扩展转速范围
 *
 * 控制策略：
 * - 根据占空比自动启动弱磁控制
 * - 占空比越高，弱磁电流越大
 * - 平滑的弱磁电流变化，避免转矩突变
 *
 * @param motor 电机状态结构体指针
 * @param dt    控制周期 (s)
 */
void foc_run_fw(motor_all_state_t *motor, float dt) {
	// 检查弱磁功能是否启用
	if (motor->m_conf->foc_fw_current_max < fmaxf(motor->m_conf->cc_min_current, 0.001)) {
		return;	// 弱磁电流设置过小，禁用弱磁功能
	}

	/*
	 * 弱磁控制条件检查
	 *
	 * 弱磁控制在以下情况下激活：
	 * 1. 电机正在运行
	 * 2. 控制模式为电流控制、电流制动或速度控制
	 * 3. 或者之前已经激活弱磁控制(允许在弱磁状态下切换控制模式)
	 */
	if (motor->m_state == MC_STATE_RUNNING &&
			(motor->m_control_mode == CONTROL_MODE_CURRENT ||
					motor->m_control_mode == CONTROL_MODE_CURRENT_BRAKE ||
					motor->m_control_mode == CONTROL_MODE_SPEED ||
					motor->m_i_fw_set > motor->m_conf->cc_min_current)) {

		float fw_current_now = 0.0;					// 当前弱磁电流
		float duty_abs = motor->m_duty_abs_filtered;	// 滤波后的占空比绝对值

		/*
		 * 基于占空比的弱磁电流计算
		 *
		 * 当占空比超过启动阈值时，线性增加弱磁电流：
		 * I_fw = map(duty, duty_start, duty_max, 0, I_fw_max)
		 *
		 * 这种方法确保：
		 * 1. 只在需要时启动弱磁(高占空比)
		 * 2. 弱磁电流与负载成比例
		 * 3. 平滑的弱磁电流变化
		 */
		if (motor->m_conf->foc_fw_duty_start < 0.99 &&
				duty_abs > motor->m_conf->foc_fw_duty_start * motor->m_conf->l_max_duty) {
			fw_current_now = utils_map(duty_abs,
					motor->m_conf->foc_fw_duty_start * motor->m_conf->l_max_duty,	// 弱磁启动占空比
					motor->m_conf->l_max_duty,										// 最大占空比
					0.0, motor->m_conf->foc_fw_current_max);						// 弱磁电流范围

			/*
			 * 弱磁退出延时保护
			 *
			 * m_current_off_delay用于防止退出弱磁后过早停止调制。
			 * 如果轴解耦工作不正常，快速改变电流时调制可能出现振荡，
			 * 这会使估计的占空比降到弱磁阈值以下足够长的时间来停止调制。
			 * 当这种情况发生时，MOSFET的体二极管会承受大电流并产生
			 * 意外的制动。因此在退出弱磁后保持调制一段时间，让振荡
			 * 在MOSFET仍被驱动时有机会衰减。
			 */
			motor->m_current_off_delay = 1.0;
		}

		/*
		 * 弱磁电流斜坡控制
		 *
		 * 为了避免弱磁电流的突变导致转矩冲击，使用斜坡函数
		 * 平滑地改变弱磁电流设定值。
		 *
		 * 斜坡时间：foc_fw_ramp_time (s)
		 * 斜坡速率：foc_fw_current_max / foc_fw_ramp_time (A/s)
		 */
		if (motor->m_conf->foc_fw_ramp_time < dt) {
			// 斜坡时间小于控制周期，直接设置目标值
			motor->m_i_fw_set = fw_current_now;
		} else {
			// 使用斜坡函数平滑改变弱磁电流
			utils_step_towards((float*)&motor->m_i_fw_set, fw_current_now,
					(dt / motor->m_conf->foc_fw_ramp_time) * motor->m_conf->foc_fw_current_max);
		}
	}
}

/**
 * 高频注入角度调整函数
 *
 * 根据HFI检测到的角度误差调整转子位置估计。
 * 使用双积分器结构提高跟踪性能和抗干扰能力。
 *
 * 控制器结构：
 * θ̇_hfi = -K1 * e_θ - K2 * ∫e_θ dt
 * 其中 e_θ 为HFI检测到的角度误差
 *
 * 双积分器的优势：
 * 1. 提高稳态精度 - 消除稳态角度误差
 * 2. 改善动态响应 - 更快的角度跟踪
 * 3. 增强鲁棒性 - 对参数变化不敏感
 *
 * @param ang_err HFI检测到的角度误差 (rad)
 * @param motor   电机状态结构体指针
 * @param dt      控制周期 (s)
 */
void foc_hfi_adjust_angle(float ang_err, motor_all_state_t *motor, float dt) {
	mc_configuration *conf = motor->m_conf;

	/*
	 * HFI控制器增益计算
	 *
	 * TODO: 检查这两个增益之间的比例是否合理，或引入独立的增益参数
	 *
	 * gain_int:  一阶积分增益 (比例-积分控制器的积分项)
	 * gain_int2: 二阶积分增益 (双积分器的第二个积分项)
	 */
	const float gain_int = 4000.0 * conf->foc_hfi_gain;	// 一阶积分增益
	const float gain_int2 = 10.0 * conf->foc_hfi_gain;		// 二阶积分增益

	/*
	 * 双积分器更新
	 *
	 * 第二个积分器：∫∫e_θ dt dt
	 * 限制积分器输出防止饱和，限制值为当前速度估计的绝对值
	 */
	motor->m_hfi.double_integrator += ang_err * gain_int2;
	utils_truncate_number_abs(&motor->m_hfi.double_integrator, fabsf(motor->m_speed_est_fast));

	/*
	 * HFI角度更新
	 *
	 * 角度修正方程：
	 * θ_hfi(k+1) = θ_hfi(k) - dt * (K1 * e_θ + K2 * ∫e_θ dt)
	 *
	 * 负号表示误差修正的方向
	 */
	motor->m_hfi.angle -= dt * (gain_int * ang_err + motor->m_hfi.double_integrator);

	// 将角度归一化到[-π, π]范围
	utils_norm_angle_rad((float*)&motor->m_hfi.angle);

	// 标记HFI已准备就绪
	motor->m_hfi.ready = true;
}

/**
 * 预计算值函数
 *
 * 预计算电机参数相关的常用值，提高实时控制性能。
 * 这些预计算值在FOC控制循环中频繁使用，预先计算可以
 * 减少实时计算负担，提高控制频率。
 *
 * 主要计算内容：
 * 1. d轴和q轴电感值
 * 2. 电感倒数相关的组合参数
 * 3. 观测器初始磁链估计值
 *
 * @param motor 电机状态结构体指针
 */
void foc_precalc_values(motor_all_state_t *motor) {
	const mc_configuration *conf_now = motor->m_conf;

	/*
	 * d轴和q轴电感计算
	 *
	 * 对于内置式永磁同步电机(IPMSM)：
	 * Ld ≠ Lq (凸极性电机)
	 *
	 * 计算公式：
	 * Lq = L + (Ld - Lq) / 2
	 * Ld = L - (Ld - Lq) / 2
	 * 其中 L 为平均电感，(Ld - Lq) 为电感差
	 */
	motor->p_lq = conf_now->foc_motor_l + conf_now->foc_motor_ld_lq_diff * 0.5;	// q轴电感
	motor->p_ld = conf_now->foc_motor_l - conf_now->foc_motor_ld_lq_diff * 0.5;	// d轴电感

	/*
	 * 电感倒数差值
	 *
	 * 在FOC控制中经常需要计算：(1/Lq - 1/Ld)
	 * 预计算此值可提高实时性能
	 */
	motor->p_inv_ld_lq = (1.0 / motor->p_lq - 1.0 / motor->p_ld);

	/*
	 * 电感倒数平均值的一半
	 *
	 * 计算：(0.5/Lq + 0.5/Ld) * 0.9
	 * 其中0.9系数用于撤销检测过程中的调整
	 * 这个值在某些FOC算法中用于电压补偿计算
	 */
	motor->p_v2_v3_inv_avg_half = (0.5 / motor->p_lq + 0.5 / motor->p_ld) * 0.9;

	/*
	 * 观测器磁链估计初始值
	 *
	 * 将配置的永磁体磁链值作为观测器的初始磁链估计
	 * 这为磁链自适应观测器提供了合理的起始点
	 */
	motor->m_observer_state.lambda_est = conf_now->foc_motor_flux_linkage;
}
